(()=>{var a={};a.id=974,a.ids=[974],a.modules={133:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(60687),e=c(43210),f=c.n(e),g=c(64398),h=c(4780);let i=({rating:a,maxRating:b=5,size:c="md",interactive:e=!1,onRatingChange:i,className:j})=>{let[k,l]=f().useState(0),m={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"},n=()=>{e&&l(0)};return(0,d.jsxs)("div",{className:(0,h.cn)("flex items-center gap-1",j),children:[Array.from({length:b},(b,f)=>{let j=f+1;return(0,d.jsx)(g.A,{className:(0,h.cn)(m[c],(b=>{let c=k||a;return b<=c?"fill-yellow-400 text-yellow-400":b-.5<=c?"fill-yellow-400/50 text-yellow-400":"fill-gray-200 text-gray-200"})(j),e&&"cursor-pointer hover:scale-110 transition-transform"),onClick:()=>{e&&i&&i(j)},onMouseEnter:()=>{e&&l(j)},onMouseLeave:n},f)}),a>0&&(0,d.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:a.toFixed(1)})]})}},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},632:(a,b,c)=>{"use strict";c.d(b,{default:()=>K});var d=c(60687),e=c(43210),f=c(30474),g=c(85814),h=c.n(g),i=c(62185),j=c(4780),k=c(133),l=c(29523),m=c(15079),n=c(89667),o=c(99270),p=c(54184),q=c(44493),r=c(96834),s=c(85726),t=c(91821),u=c(56085),v=c(41312),w=c(78200),x=c(25541),y=c(96882),z=c(78122),A=c(11860),B=c(64398),C=c(33872);let D=(0,c(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var E=c(16189),F=c(29867);let G={hybrid:u.A,collaborative:v.A,content:w.A,trending:x.A},H={hybrid:"Combines multiple recommendation techniques for best results",collaborative:"Based on what similar users liked",content:"Based on expert characteristics and your preferences",trending:"Currently popular experts on the platform"};function I({limit:a=10,algorithm:b="hybrid",category:c,exclude:f=[],title:g="Recommended for You",description:h="Experts we think you'll love",showAlgorithmInfo:i=!0,showRefreshButton:j=!0,className:k=""}){let m=(0,E.useRouter)(),{toast:n}=(0,F.d)(),[o,v]=(0,e.useState)(new Set),{recommendations:w,loading:x,error:I,algorithmUsed:J,totalAvailable:K,isFallback:L,lastUpdated:M,refresh:N,trackClick:O,trackDismiss:P,trackFavorite:Q,trackChatStart:R}=(0,p.GI)({limit:a,algorithm:b,category:c,exclude:[...f,...Array.from(o)]}),S=async(a,b)=>{await O(a,b),m.push(`/experts/${a}`)},T=async(a,b,c)=>{c.stopPropagation(),await R(a,b),m.push(`/chat?expertId=${a}`)},U=async(a,b,c)=>{c.stopPropagation(),v(b=>new Set([...b,a])),await P(a,b,"user_dismissed"),n({title:"Expert dismissed",description:"We'll show you different recommendations."})},V=async(a,b,c)=>{c.stopPropagation(),await Q(a,b),n({title:"Added to favorites",description:"This expert has been added to your favorites."})},W=async()=>{v(new Set),await N(),n({title:"Recommendations refreshed",description:"We've updated your recommendations."})},X=G[J]||u.A;return x?(0,d.jsxs)("div",{className:`space-y-4 ${k}`,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(s.E,{className:"h-6 w-48 mb-2"}),(0,d.jsx)(s.E,{className:"h-4 w-64"})]}),j&&(0,d.jsx)(s.E,{className:"h-9 w-24"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Array.from({length:a}).map((a,b)=>(0,d.jsxs)(q.Zp,{className:"h-48",children:[(0,d.jsxs)(q.aR,{children:[(0,d.jsx)(s.E,{className:"h-5 w-3/4"}),(0,d.jsx)(s.E,{className:"h-4 w-full"})]}),(0,d.jsx)(q.Wu,{children:(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(s.E,{className:"h-4 w-1/2"}),(0,d.jsx)(s.E,{className:"h-4 w-2/3"}),(0,d.jsx)(s.E,{className:"h-8 w-full"})]})})]},b))})]}):I&&!L?(0,d.jsx)("div",{className:k,children:(0,d.jsxs)(t.Fc,{children:[(0,d.jsx)(y.A,{className:"h-4 w-4"}),(0,d.jsxs)(t.TN,{children:[I,". ",j&&(0,d.jsx)(l.$,{variant:"link",className:"p-0 h-auto",onClick:W,children:"Try again"})]})]})}):0===w.length?(0,d.jsx)("div",{className:k,children:(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(X,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No recommendations available"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"We don't have enough data to make personalized recommendations yet."}),j&&(0,d.jsxs)(l.$,{onClick:W,variant:"outline",children:[(0,d.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Try Again"]})]})}):(0,d.jsxs)("div",{className:`space-y-4 ${k}`,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold",children:g}),L&&(0,d.jsx)(r.E,{variant:"secondary",className:"text-xs",children:"Fallback"})]}),(0,d.jsx)("p",{className:"text-muted-foreground text-sm",children:h}),i&&(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-2 text-xs text-muted-foreground",children:[(0,d.jsx)(X,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:H[J]}),K>w.length&&(0,d.jsxs)("span",{children:["• ",K-w.length," more available"]})]})]}),j&&(0,d.jsxs)(l.$,{onClick:W,variant:"outline",size:"sm",disabled:x,children:[(0,d.jsx)(z.A,{className:`h-4 w-4 mr-2 ${x?"animate-spin":""}`}),"Refresh"]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:w.map((a,b)=>(0,d.jsxs)(q.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow relative group",onClick:()=>S(a.expert_id,b),children:[(0,d.jsx)(l.$,{variant:"ghost",size:"sm",className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10 h-6 w-6 p-0",onClick:c=>U(a.expert_id,b,c),children:(0,d.jsx)(A.A,{className:"h-3 w-3"})}),(0,d.jsxs)(q.aR,{className:"pb-3",children:[(0,d.jsx)("div",{className:"flex items-start justify-between",children:(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)(q.ZB,{className:"text-base line-clamp-1",children:a.name}),(0,d.jsx)(q.BT,{className:"line-clamp-2 text-xs mt-1",children:a.description})]})}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,d.jsx)(r.E,{variant:"secondary",className:"text-xs",children:a.category}),(0,d.jsxs)("div",{className:"text-xs text-muted-foreground",children:[Math.round(100*a.score),"% match"]})]})]}),(0,d.jsxs)(q.Wu,{className:"pt-0",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(B.A,{className:"h-3 w-3 fill-current text-yellow-400"}),(0,d.jsx)("span",{children:a.average_rating.toFixed(1)})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(C.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:a.total_chats})]}),(0,d.jsxs)("div",{className:"text-xs",children:[a.price_per_message,"% fee"]})]}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground mb-3 line-clamp-2",children:a.reason}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)(l.$,{size:"sm",className:"flex-1",onClick:c=>T(a.expert_id,b,c),children:[(0,d.jsx)(C.A,{className:"h-3 w-3 mr-1"}),"Chat"]}),(0,d.jsx)(l.$,{size:"sm",variant:"outline",onClick:c=>V(a.expert_id,b,c),children:(0,d.jsx)(D,{className:"h-3 w-3"})})]})]})]},a.expert_id))}),M&&(0,d.jsxs)("p",{className:"text-xs text-muted-foreground text-center",children:["Last updated: ",new Date(M).toLocaleString()]})]})}var J=c(63213);let K=()=>{let{user:a}=(0,J.A)(),[b,c]=(0,e.useState)([]),[g,p]=(0,e.useState)([]),[q,r]=(0,e.useState)(!0),[s,t]=(0,e.useState)(null),[u,v]=(0,e.useState)(""),[w,x]=(0,e.useState)("name"),[y,z]=(0,e.useState)("all"),A=async()=>{try{r(!0),t(null);let a=await i.FH.getPublicExperts();a.success?(c(a.experts),p(a.experts)):t(a.error||"Failed to load experts")}catch(a){t(a.message||"Failed to load experts")}finally{r(!1)}};return((0,e.useEffect)(()=>{A()},[]),(0,e.useEffect)(()=>{let a=[...b];if(u&&(a=a.filter(a=>a.name.toLowerCase().includes(u.toLowerCase())||a.description.toLowerCase().includes(u.toLowerCase())||a.labels.some(a=>a.toLowerCase().includes(u.toLowerCase())))),"all"!==y){let b=parseFloat(y);a=a.filter(a=>a.averageRating&&a.averageRating>=b)}a.sort((a,b)=>{switch(w){case"rating":return(b.averageRating||0)-(a.averageRating||0);case"reviews":return(b.totalReviews||0)-(a.totalReviews||0);case"chats":return(b.totalChats||0)-(a.totalChats||0);case"newest":return new Date(b.createdAt).getTime()-new Date(a.createdAt).getTime();default:return a.name.localeCompare(b.name)}}),p(a)},[b,u,w,y]),q)?(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-lg border border-gray-100 animate-pulse",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-2"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3"})]})]}),(0,d.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"})]}),(0,d.jsx)("div",{className:"h-10 bg-gray-200 rounded-lg"})]},b))}):s?(0,d.jsx)("div",{className:"text-center py-12",children:(0,d.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-6 max-w-md mx-auto",children:[(0,d.jsx)("div",{className:"text-red-600 text-4xl mb-4",children:"⚠️"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:"Unable to Load Experts"}),(0,d.jsx)("p",{className:"text-red-600 mb-4",children:s}),(0,d.jsx)("button",{onClick:A,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Try Again"})]})}):0===b.length?(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-700 mb-2",children:"No Public Experts Available"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Check back soon for new AI experts!"})]}):(0,d.jsxs)("div",{className:"space-y-8",children:[a&&(0,d.jsx)(I,{limit:6,algorithm:"hybrid",exclude:g.map(a=>a.id),title:"Recommended for You",description:"AI experts we think you'll love based on your preferences",showAlgorithmInfo:!0,showRefreshButton:!0,className:"mb-8"}),(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,d.jsxs)("div",{className:"flex-1 relative",children:[(0,d.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,d.jsx)(n.p,{type:"text",placeholder:"Search experts by name, description, or tags...",value:u,onChange:a=>v(a.target.value),className:"pl-10"})]}),(0,d.jsx)("div",{className:"w-full lg:w-48",children:(0,d.jsxs)(m.l6,{value:w,onValueChange:x,children:[(0,d.jsx)(m.bq,{children:(0,d.jsx)(m.yv,{placeholder:"Sort by"})}),(0,d.jsxs)(m.gC,{children:[(0,d.jsx)(m.eb,{value:"name",children:"Name (A-Z)"}),(0,d.jsx)(m.eb,{value:"rating",children:"Highest Rated"}),(0,d.jsx)(m.eb,{value:"reviews",children:"Most Reviews"}),(0,d.jsx)(m.eb,{value:"chats",children:"Most Popular"}),(0,d.jsx)(m.eb,{value:"newest",children:"Newest First"})]})]})}),(0,d.jsx)("div",{className:"w-full lg:w-48",children:(0,d.jsxs)(m.l6,{value:y,onValueChange:z,children:[(0,d.jsx)(m.bq,{children:(0,d.jsx)(m.yv,{placeholder:"Filter by rating"})}),(0,d.jsxs)(m.gC,{children:[(0,d.jsx)(m.eb,{value:"all",children:"All Ratings"}),(0,d.jsx)(m.eb,{value:"4",children:"4+ Stars"}),(0,d.jsx)(m.eb,{value:"3",children:"3+ Stars"}),(0,d.jsx)(m.eb,{value:"2",children:"2+ Stars"})]})]})})]}),(0,d.jsxs)("div",{className:"mt-4 flex items-center justify-between text-sm text-gray-600",children:[(0,d.jsxs)("span",{children:["Showing ",g.length," of ",b.length," experts"]}),(u||"all"!==y)&&(0,d.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{v(""),z("all")},children:"Clear Filters"})]})]}),0===g.length&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-700 mb-2",children:"No Experts Found"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Try adjusting your search or filters"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map(a=>(0,d.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl hover:scale-[1.02] transition-all duration-300 group",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,d.jsxs)("div",{className:"relative",children:[a.imageUrl?(0,d.jsx)(f.default,{src:(0,j.L)(a.imageUrl),alt:a.name,width:64,height:64,className:"w-16 h-16 object-cover rounded-full border-2 border-gray-100"}):(0,d.jsx)("div",{className:"w-16 h-16 rounded-full flex items-center justify-center text-2xl text-white shadow-lg",style:{backgroundColor:"#1E3A8A"},children:(a=>a.includes("business")||a.includes("marketing")?"\uD83D\uDCBC":a.includes("code")||a.includes("programming")?"\uD83D\uDCBB":a.includes("creative")||a.includes("design")?"\uD83C\uDFA8":a.includes("education")||a.includes("learning")?"\uD83D\uDCDA":a.includes("health")||a.includes("medical")?"\uD83C\uDFE5":a.includes("finance")||a.includes("money")?"\uD83D\uDCB0":"\uD83E\uDD16")(a.labels)}),(0,d.jsx)("div",{className:"absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center",children:(0,d.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})})]}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-bold text-lg text-gray-900 group-hover:text-blue-900 transition-colors",children:a.name}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,d.jsx)("span",{className:"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full font-medium",children:a.model}),(0,d.jsx)("span",{className:"text-xs text-green-600 font-medium",children:"● Online"})]}),a.averageRating&&a.averageRating>0?(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(k.A,{rating:a.averageRating,size:"sm"}),(0,d.jsxs)("span",{className:"text-xs text-gray-500",children:["(",a.totalReviews," review",1!==a.totalReviews?"s":"",")"]})]}):(0,d.jsx)("div",{className:"text-xs text-gray-400",children:"No reviews yet"})]})]}),(0,d.jsx)("p",{className:"text-gray-600 text-sm mb-4 leading-relaxed",children:((a,b=120)=>a.length<=b?a:a.substring(0,b)+"...")(a.description)}),a.labels&&a.labels.length>0&&(0,d.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[a.labels.slice(0,3).map((a,b)=>(0,d.jsxs)("span",{className:"inline-block px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full font-medium hover:bg-gray-200 transition-colors",children:["#",a]},b)),a.labels.length>3&&(0,d.jsxs)("span",{className:"inline-block px-3 py-1 text-xs text-gray-500 rounded-full",children:["+",a.labels.length-3," more"]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(h(),{href:`/expert/${a.id}`,className:"block w-full py-3 px-4 text-center font-semibold rounded-xl transition-all duration-200 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",style:{backgroundColor:"#1E3A8A"},children:"View Profile"}),(0,d.jsx)(h(),{href:`/chat?expertId=${a.id}`,className:"block w-full py-3 px-4 text-center font-semibold rounded-xl border-2 transition-all duration-200 hover:shadow-md",style:{borderColor:"#1E3A8A",color:"#1E3A8A"},children:"⚡ Start Chat"})]}),(0,d.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-100",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500 mb-2",children:[(0,d.jsxs)("span",{children:["\uD83D\uDCB0 ",a.pricingPercentage,"% of usage"]}),(0,d.jsx)("span",{children:"\uD83D\uDD52 Instant response"})]}),a.totalChats&&a.totalChats>0&&(0,d.jsxs)("div",{className:"text-xs text-gray-400",children:["\uD83D\uDCAC ",a.totalChats," conversation",1!==a.totalChats?"s":""]})]})]},a.id))})]})}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(a,b,c)=>{"use strict";c.d(b,{L:()=>h,cn:()=>g});var d=c(49384),e=c(82348);let f=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001";function g(...a){return(0,e.QP)((0,d.$)(a))}function h(a){return`${f}/${a.startsWith("/")?a.replace("/",""):a}`}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:a=>{"use strict";a.exports=require("assert")},15079:(a,b,c)=>{"use strict";c.d(b,{bq:()=>j,eb:()=>m,gC:()=>l,l6:()=>i,yv:()=>k});var d=c(60687),e=c(43210),f=c(78272),g=c(4780);let h=e.createContext({open:!1,setOpen:()=>{}}),i=({value:a,onValueChange:b,children:c})=>{let[f,g]=e.useState(!1);return(0,d.jsx)(h.Provider,{value:{value:a,onValueChange:b,open:f,setOpen:g},children:(0,d.jsx)("div",{className:"relative",children:c})})},j=({className:a,children:b})=>{let{open:c,setOpen:i}=e.useContext(h);return(0,d.jsxs)("button",{type:"button",className:(0,g.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),onClick:()=>i(!c),children:[b,(0,d.jsx)(f.A,{className:"h-4 w-4 opacity-50"})]})},k=({placeholder:a,className:b})=>{let{value:c}=e.useContext(h);return(0,d.jsx)("span",{className:(0,g.cn)("block truncate",b),children:c||a})},l=({className:a,children:b})=>{let{open:c,setOpen:f}=e.useContext(h);return c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>f(!1)}),(0,d.jsx)("div",{className:(0,g.cn)("absolute z-50 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-white p-1 text-gray-950 shadow-md animate-in fade-in-0 zoom-in-95",a),children:b})]}):null},m=({value:a,className:b,children:c,onSelect:f})=>{let{onValueChange:i,setOpen:j}=e.useContext(h);return(0,d.jsx)("div",{className:(0,g.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-gray-100 focus:bg-gray-100 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",b),onClick:()=>{i?.(a),j(!1),f?.()},children:c})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(37413),e=c(41857);function f(){return(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:[(0,d.jsxs)("section",{className:"relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-900/5 to-indigo-900/5"}),(0,d.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 py-20",children:(0,d.jsxs)("div",{className:"text-center space-y-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h1",{className:"text-5xl md:text-7xl font-bold bg-gradient-to-r from-blue-900 via-blue-800 to-indigo-900 bg-clip-text text-transparent",children:"AI Expert Marketplace"}),(0,d.jsx)("p",{className:"text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Connect with specialized AI experts tailored to your needs. From business consulting to creative solutions."})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,d.jsx)("div",{className:"px-6 py-3 bg-white rounded-full shadow-lg border border-gray-100",children:(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:["✨ Over ",(0,d.jsx)("strong",{className:"font-semibold",style:{color:"#1E3A8A"},children:"50+ AI Experts"})," Available"]})}),(0,d.jsx)("div",{className:"px-6 py-3 bg-white rounded-full shadow-lg border border-gray-100",children:(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:["\uD83D\uDE80 ",(0,d.jsx)("strong",{className:"font-semibold",style:{color:"#1E3A8A"},children:"Instant"})," Responses"]})})]})]})})]}),(0,d.jsxs)("section",{className:"max-w-7xl mx-auto px-4 py-16",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Featured AI Experts"}),(0,d.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Discover our curated selection of top-performing AI experts, each specialized in their domain"})]}),(0,d.jsx)(e.default,{})]}),(0,d.jsx)("section",{className:"bg-white py-20",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4",children:[(0,d.jsx)("div",{className:"text-center mb-16",children:(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Why Choose Our Platform?"})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"text-center p-8 rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-100",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl",style:{backgroundColor:"#1E3A8A",color:"white"},children:"\uD83C\uDFAF"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Specialized Expertise"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Each AI expert is fine-tuned for specific domains, ensuring highly relevant and accurate responses."})]}),(0,d.jsxs)("div",{className:"text-center p-8 rounded-2xl bg-gradient-to-br from-green-50 to-emerald-50 border border-green-100",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl",style:{backgroundColor:"#1E3A8A",color:"white"},children:"⚡"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Instant Access"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Start conversations immediately. No waiting, no appointments - just instant expert guidance."})]}),(0,d.jsxs)("div",{className:"text-center p-8 rounded-2xl bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-100",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl",style:{backgroundColor:"#1E3A8A",color:"white"},children:"\uD83D\uDD12"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Secure & Private"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Your conversations are encrypted and private. We prioritize your data security above all."})]})]})]})})]})}},21820:a=>{"use strict";a.exports=require("os")},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},29867:(a,b,c)=>{"use strict";c.d(b,{d:()=>i});var d=c(43210);let e={toasts:[]},f=[];function g(){f.forEach(a=>a(e))}function h(a){let b=e.toasts.findIndex(b=>b.id===a);b>-1&&(e.toasts.splice(b,1),g())}function i(){let[a,b]=(0,d.useState)(e),c=(0,d.useCallback)(a=>(f.push(a),()=>{f=f.filter(b=>b!==a)}),[]),i=(0,d.useCallback)(a=>(function(a){let b=Math.random().toString(36).substr(2,9),c={id:b,duration:5e3,...a};return e.toasts.push(c),g(),c.duration&&c.duration>0&&setTimeout(()=>{h(b)},c.duration),b})(a),[]),j=(0,d.useCallback)(a=>{h(a)},[]);return(0,d.useState)(()=>c(b)),{toast:i,dismiss:j,toasts:a.toasts}}},33872:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},38421:(a,b,c)=>{Promise.resolve().then(c.bind(c,632))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41857:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\Web\\\\pakarai\\\\ai-trainer\\\\fe\\\\src\\\\components\\\\ExpertMarketplace.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertMarketplace.tsx","default")},42393:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,21204)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},54184:(a,b,c)=>{"use strict";c.d(b,{GI:()=>h,uP:()=>i});var d=c(43210),e=c(62185);class f{async getPersonalizedRecommendations(a={}){try{let b=new URLSearchParams;return a.limit&&b.append("limit",a.limit.toString()),a.algorithm&&b.append("algorithm",a.algorithm),a.category&&b.append("category",a.category),a.exclude&&a.exclude.length>0&&b.append("exclude",a.exclude.join(",")),(await e.FH.get(`${this.baseUrl}/personalized?${b.toString()}`)).data.data}catch(a){return console.error("Failed to get personalized recommendations:",a),{recommendations:[],algorithm_used:"fallback",total_available:0,cache_hit:!1,generated_at:new Date().toISOString(),fallback:!0,error:a.response?.data?.message||"Service unavailable"}}}async getSimilarExperts(a,b=10){try{return(await e.FH.get(`${this.baseUrl}/similar/${a}?limit=${b}`)).data.data}catch(b){return console.error("Failed to get similar experts:",b),{similar_experts:[],reference_expert:{id:a,name:"Unknown Expert"}}}}async refreshRecommendations(a="hybrid"){try{return await e.FH.post(`${this.baseUrl}/refresh`,{body:{algorithm:a}}),!0}catch(a){return console.error("Failed to refresh recommendations:",a),!1}}async trackInteraction(a){try{return await e.FH.post(`${this.baseUrl}/track`,{body:a}),!0}catch(a){return console.error("Failed to track interaction:",a),!1}}async getServiceHealth(){try{return(await e.FH.get(`${this.baseUrl}/health`)).data.data}catch(a){return console.error("Failed to get service health:",a),{status:"unhealthy",error:a.response?.data?.message||"Service unavailable"}}}async trackRecommendationClick(a,b,c,d){await this.trackInteraction({expert_id:a,interaction_type:"click",recommendation_position:b,algorithm_used:c,metadata:d})}async trackRecommendationDismiss(a,b,c,d){await this.trackInteraction({expert_id:a,interaction_type:"dismiss",recommendation_position:b,algorithm_used:c,metadata:{dismiss_reason:d}})}async trackRecommendationFavorite(a,b,c){await this.trackInteraction({expert_id:a,interaction_type:"favorite",recommendation_position:b,algorithm_used:c})}async trackRecommendationChatStart(a,b,c){await this.trackInteraction({expert_id:a,interaction_type:"chat_start",recommendation_position:b,algorithm_used:c})}constructor(){this.baseUrl="/api/recommendations"}}let g=new f;function h(a={}){let[b,c]=(0,d.useState)([]),[e,f]=(0,d.useState)(!0),[i,j]=(0,d.useState)(null),[k,l]=(0,d.useState)("hybrid"),[m,n]=(0,d.useState)(0),[o,p]=(0,d.useState)(!1),[q,r]=(0,d.useState)(null),s=(0,d.useCallback)(async()=>{try{f(!0),j(null);let b=await g.getPersonalizedRecommendations(a);c(b.recommendations),l(b.algorithm_used),n(b.total_available),p(b.fallback||!1),r(b.generated_at),b.error&&j(b.error)}catch(a){j(a.message||"Failed to load recommendations"),c([])}finally{f(!1)}},[a]),t=(0,d.useCallback)(async()=>{await s()},[s]),u=(0,d.useCallback)(async(a,b)=>{await g.trackRecommendationClick(a,b,k)},[k]),v=(0,d.useCallback)(async(a,b,c)=>{await g.trackRecommendationDismiss(a,b,k,c)},[k]),w=(0,d.useCallback)(async(a,b)=>{await g.trackRecommendationFavorite(a,b,k)},[k]),x=(0,d.useCallback)(async(a,b)=>{await g.trackRecommendationChatStart(a,b,k)},[k]);return{recommendations:b,loading:e,error:i,algorithmUsed:k,totalAvailable:m,isFallback:o,lastUpdated:q,refresh:t,trackClick:u,trackDismiss:v,trackFavorite:w,trackChatStart:x}}function i(a){let[b,c]=(0,d.useState)([]),[e,f]=(0,d.useState)(!1),[h,i]=(0,d.useState)(null),[j,k]=(0,d.useState)(null),l=(0,d.useCallback)(async()=>{if(a.expertId&&!1!==a.enabled)try{f(!0),i(null);let b=await g.getSimilarExperts(a.expertId,a.limit||10);c(b.similar_experts),k(b.reference_expert)}catch(a){i(a.message||"Failed to load similar experts"),c([])}finally{f(!1)}},[a.expertId,a.limit,a.enabled]);return{similarExperts:b,loading:e,error:h,referenceExpert:j,refresh:(0,d.useCallback)(async()=>{await l()},[l]),trackClick:(0,d.useCallback)(async(b,c)=>{await g.trackInteraction({expert_id:b,interaction_type:"click",recommendation_position:c,algorithm_used:"similar",metadata:{reference_expert_id:a.expertId}})},[a.expertId])}}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},56085:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},74075:a=>{"use strict";a.exports=require("zlib")},78122:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78200:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},85726:(a,b,c)=>{"use strict";c.d(b,{E:()=>f});var d=c(60687),e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{className:(0,e.cn)("animate-pulse rounded-md bg-muted",a),...b})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>f});var d=c(60687);c(43210);var e=c(4780);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}},91645:a=>{"use strict";a.exports=require("net")},91821:(a,b,c)=>{"use strict";c.d(b,{Fc:()=>i,TN:()=>k,XL:()=>j});var d=c(60687),e=c(43210),f=c(24224),g=c(4780);let h=(0,f.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),i=e.forwardRef(({className:a,variant:b,...c},e)=>(0,d.jsx)("div",{ref:e,role:"alert",className:(0,g.cn)(h({variant:b}),a),...c}));i.displayName="Alert";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h5",{ref:c,className:(0,g.cn)("mb-1 font-medium leading-none tracking-tight",a),...b}));j.displayName="AlertTitle";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,g.cn)("text-sm [&_p]:leading-relaxed",a),...b}));k.displayName="AlertDescription"},94735:a=>{"use strict";a.exports=require("events")},96565:(a,b,c)=>{Promise.resolve().then(c.bind(c,41857))},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(4780);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},96882:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},99270:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,842,928,474,263],()=>b(b.s=42393));module.exports=c})();