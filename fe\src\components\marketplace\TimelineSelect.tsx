"use client";

import React from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TimelineOption, TimelineType } from "@/types/filters";
import { cn } from "@/lib/utils";

interface TimelineSelectProps {
  options: TimelineOption[];
  selectedValue: TimelineType;
  onSelect: (value: TimelineType) => void;
  className?: string;
  disabled?: boolean;
}

export const TimelineSelect: React.FC<TimelineSelectProps> = ({
  options,
  selectedValue,
  onSelect,
  className = "",
  disabled = false
}) => {
  const handleValueChange = (value: string) => {
    onSelect(value as TimelineType);
  };

  return (
    <Select
      value={selectedValue}
      onValueChange={handleValueChange}
      disabled={disabled}
    >
      <SelectTrigger
        className={cn(
          "w-full sm:w-[150px] h-10 bg-white border border-gray-200 hover:border-gray-300 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        aria-label="Select timeline"
      >
        <SelectValue placeholder="Last 30 Days" />
      </SelectTrigger>

      <SelectContent
        className="bg-white border border-gray-200 shadow-lg rounded-lg p-1 min-w-[150px]"
      >
        {options.map((option) => (
          <SelectItem
            key={option.id}
            value={option.id}
            className={cn(
              "px-3 py-2 rounded-md cursor-pointer transition-colors duration-150",
              "hover:bg-gray-50 focus:bg-gray-50 focus:outline-none",
              "data-[state=checked]:bg-primary/10 data-[state=checked]:text-primary",
              selectedValue === option.id && "bg-primary/5"
            )}
          >
            <span className="font-medium text-gray-900 text-sm">
              {option.label}
            </span>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};