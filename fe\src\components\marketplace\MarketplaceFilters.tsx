"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { FilterSelect } from "./FilterSelect";
import { TimelineSelect } from "./TimelineSelect";
import { useFilters } from "@/hooks/useFilters";
import { FILTER_OPTIONS, TIMELINE_OPTIONS } from "@/types/filters";
import { Search, X, RotateCcw } from "lucide-react";
import { cn } from "@/lib/utils";

interface MarketplaceFiltersProps {
  className?: string;
  onFiltersChange?: () => void;
}

export const MarketplaceFilters: React.FC<MarketplaceFiltersProps> = ({
  className = "",
  onFiltersChange
}) => {
  const { state, actions } = useFilters();
  const [searchInput, setSearchInput] = useState(state.searchQuery);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Get the selected filter option to determine if timeline should be shown
  const selectedFilterOption = FILTER_OPTIONS.find(option => option.id === state.selectedFilter);
  const showTimeline = selectedFilterOption?.hasTimeline || false;

  // Debounced search handling
  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      actions.setSearchQuery(searchInput);
    }, 300); // 300ms debounce

    setSearchTimeout(timeout);

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [searchInput, actions, searchTimeout]);

  // Notify parent when filters change
  useEffect(() => {
    if (onFiltersChange) {
      onFiltersChange();
    }
  }, [state.selectedFilter, state.selectedTimeline, state.searchQuery, onFiltersChange]);

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  const clearSearch = () => {
    setSearchInput('');
    actions.setSearchQuery('');
  };

  const resetAllFilters = () => {
    setSearchInput('');
    actions.resetFilters();
  };

  const hasActiveFilters = state.searchQuery ||
    state.selectedFilter !== 'recommended' ||
    state.selectedTimeline !== 'last-30-days';

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">
          Find AI Experts
        </h2>

        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={resetAllFilters}
            className="text-gray-500 hover:text-gray-700"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset Filters
          </Button>
        )}
      </div>

      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="text"
          placeholder="Search experts by name, description, or skills..."
          value={searchInput}
          onChange={handleSearchInputChange}
          className="pl-10 pr-10 h-12 bg-white border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20"
        />
        {searchInput && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearSearch}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-3 sm:items-center">
        <div className="flex-1">
          <FilterSelect
            options={FILTER_OPTIONS}
            selectedValue={state.selectedFilter}
            onSelect={actions.setFilter}
            disabled={state.isLoading}
            className="w-full"
          />
        </div>

        {showTimeline && (
          <div className="sm:w-auto">
            <TimelineSelect
              options={TIMELINE_OPTIONS}
              selectedValue={state.selectedTimeline}
              onSelect={actions.setTimeline}
              disabled={state.isLoading}
              className="w-full sm:w-[150px]"
            />
          </div>
        )}
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 text-sm text-gray-600">
          <span className="font-medium">Active filters:</span>

          {state.selectedFilter !== 'recommended' && (
            <span className="inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md">
              {selectedFilterOption?.label}
            </span>
          )}

          {showTimeline && state.selectedTimeline !== 'last-30-days' && (
            <span className="inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md">
              {TIMELINE_OPTIONS.find(opt => opt.id === state.selectedTimeline)?.label}
            </span>
          )}

          {state.searchQuery && (
            <span className="inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md">
              Search: "{state.searchQuery}"
            </span>
          )}
        </div>
      )}
    </div>
  );
};