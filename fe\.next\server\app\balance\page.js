(()=>{var a={};a.id=977,a.ids=[977],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(a,b,c)=>{"use strict";c.d(b,{L:()=>h,cn:()=>g});var d=c(49384),e=c(82348);let f=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001";function g(...a){return(0,e.QP)((0,d.$)(a))}function h(a){return`${f}/${a.startsWith("/")?a.replace("/",""):a}`}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},23562:(a,b,c)=>{"use strict";c.d(b,{k:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,value:b=0,max:c=100,...e},g)=>{let h=Math.min(100,Math.max(0,b/c*100));return(0,d.jsx)("div",{ref:g,className:(0,f.cn)("relative h-4 w-full overflow-hidden rounded-full bg-gray-200",a),...e,children:(0,d.jsx)("div",{className:"h-full bg-blue-600 transition-all duration-300 ease-in-out",style:{width:`${h}%`}})})});g.displayName="Progress"},23928:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},33331:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["balance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,93251)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\balance\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\balance\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/balance/page",pathname:"/balance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/balance/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42071:(a,b,c)=>{Promise.resolve().then(c.bind(c,93251))},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},80428:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},85778:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},93251:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\Web\\\\pakarai\\\\ai-trainer\\\\fe\\\\src\\\\app\\\\balance\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\balance\\page.tsx","default")},94735:a=>{"use strict";a.exports=require("events")},94762:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>y});var d=c(60687),e=c(43210),f=c(44493),g=c(29523),h=c(23562),i=c(4780);let j=e.createContext(null),k=e.forwardRef(({defaultValue:a,value:b,onValueChange:c,className:f,children:g,...h},k)=>{let[l,m]=e.useState(a||"");return(0,d.jsx)(j.Provider,{value:{value:b||l,onValueChange:a=>{b||m(a),c?.(a)}},children:(0,d.jsx)("div",{ref:k,className:(0,i.cn)("w-full",f),...h,children:g})})});k.displayName="Tabs";let l=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsx)("div",{ref:e,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-gray-100 p-1 text-gray-500",a),...c,children:b}));l.displayName="TabsList";let m=e.forwardRef(({value:a,className:b,children:c,...f},g)=>{let h=e.useContext(j);if(!h)throw Error("TabsTrigger must be used within a Tabs component");let k=h.value===a;return(0,d.jsx)("button",{ref:g,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",k?"bg-white text-slate-950 shadow-sm":"text-slate-500 hover:text-slate-900",b),onClick:()=>h.onValueChange(a),...f,children:c})});m.displayName="TabsTrigger";let n=e.forwardRef(({value:a,className:b,children:c,...f},g)=>{let h=e.useContext(j);if(!h)throw Error("TabsContent must be used within a Tabs component");return h.value!==a?null:(0,d.jsx)("div",{ref:g,className:(0,i.cn)("mt-2 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2",b),...f,children:c})});n.displayName="TabsContent";var o=c(62185),p=c(80428),q=c(85778),r=c(23928),s=c(62688);let t=(0,s.A)("coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]]);var u=c(35583),v=c(96474);let w=(0,s.A)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);function x({showActions:a=!0,compact:b=!1,className:c=""}){let[i,j]=(0,e.useState)(null),[s,x]=(0,e.useState)([]),[y,z]=(0,e.useState)([]),[A,B]=(0,e.useState)(!0),[C,D]=(0,e.useState)(null),E=async()=>{try{B(!0);let a=await (0,o.H2)("/api/balance/summary");if(a.success)j(a.data.balance),x(a.data.recentPointTransactions),z(a.data.recentCreditTransactions);else throw Error(a.message||"Failed to fetch balance")}catch(a){D(a.message),console.error("Error fetching balance:",a)}finally{B(!1)}},F=a=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0,maximumFractionDigits:0}).format(a),G=a=>{switch(a){case"EARNED":case"PURCHASED":return"text-green-600";case"USED":return"text-red-600";case"ADMIN_ADDED":return"text-blue-600";case"ADMIN_DEDUCTED":return"text-orange-600";default:return"text-gray-600"}},H=a=>{switch(a){case"EARNED":return(0,d.jsx)(p.A,{className:"w-4 h-4 text-green-600"});case"PURCHASED":return(0,d.jsx)(q.A,{className:"w-4 h-4 text-blue-600"});case"USED":return(0,d.jsx)(r.A,{className:"w-4 h-4 text-red-600"});default:return(0,d.jsx)(t,{className:"w-4 h-4 text-gray-600"})}};if(A)return(0,d.jsx)("div",{className:`space-y-4 ${c}`,children:(0,d.jsx)(f.Zp,{children:(0,d.jsx)(f.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"})]})})})});if(C)return(0,d.jsx)(f.Zp,{className:c,children:(0,d.jsx)(f.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"text-center text-red-600",children:[(0,d.jsxs)("p",{children:["Error loading balance: ",C]}),(0,d.jsx)(g.$,{onClick:E,variant:"outline",className:"mt-2",children:"Retry"})]})})});if(!i)return null;if(b)return(0,d.jsx)(f.Zp,{className:`bg-gradient-to-r from-blue-50 to-purple-50 ${c}`,children:(0,d.jsx)(f.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-full",children:(0,d.jsx)(u.A,{className:"w-5 h-5 text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Balance"}),(0,d.jsx)("p",{className:"text-lg font-bold text-gray-900",children:F(i.totalBalance)})]})]}),(0,d.jsxs)("div",{className:"text-right text-xs text-gray-500",children:[(0,d.jsxs)("p",{children:[F(i.pointBalance)," points"]}),(0,d.jsxs)("p",{children:[F(i.creditBalance)," credits"]})]})]})})});let I=i.totalBalance>0?i.pointBalance/i.totalBalance*100:0;return(0,d.jsxs)("div",{className:`space-y-6 ${c}`,children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsx)(f.Zp,{className:"bg-gradient-to-r from-blue-50 to-blue-100",children:(0,d.jsxs)(f.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-blue-600",children:"Total Balance"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-blue-900",children:F(i.totalBalance)})]}),(0,d.jsx)("div",{className:"p-3 bg-blue-200 rounded-full",children:(0,d.jsx)(u.A,{className:"w-6 h-6 text-blue-600"})})]}),(0,d.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsxs)("span",{className:"text-blue-600",children:["Points: ",F(i.pointBalance)]}),(0,d.jsxs)("span",{className:"text-blue-600",children:[I.toFixed(1),"%"]})]}),(0,d.jsx)(h.k,{value:I,className:"h-2"}),(0,d.jsxs)("p",{className:"text-xs text-blue-500",children:["Credits: ",F(i.creditBalance)]})]})]})}),(0,d.jsx)(f.Zp,{className:"bg-gradient-to-r from-green-50 to-green-100",children:(0,d.jsxs)(f.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-green-600",children:"Points (Bonus)"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-green-900",children:F(i.pointBalance)})]}),(0,d.jsx)("div",{className:"p-3 bg-green-200 rounded-full",children:(0,d.jsx)(p.A,{className:"w-6 h-6 text-green-600"})})]}),(0,d.jsx)("div",{className:"mt-4",children:(0,d.jsxs)("p",{className:"text-xs text-green-600",children:["Total earned: ",F(i.totalPointsEarned)]})})]})}),(0,d.jsx)(f.Zp,{className:"bg-gradient-to-r from-purple-50 to-purple-100",children:(0,d.jsxs)(f.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-purple-600",children:"Credits (Paid)"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-purple-900",children:F(i.creditBalance)})]}),(0,d.jsx)("div",{className:"p-3 bg-purple-200 rounded-full",children:(0,d.jsx)(q.A,{className:"w-6 h-6 text-purple-600"})})]}),(0,d.jsx)("div",{className:"mt-4",children:(0,d.jsxs)("p",{className:"text-xs text-purple-600",children:["Total purchased: ",F(i.totalCreditsPurchased)]})})]})})]}),a&&(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsxs)(f.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(v.A,{className:"w-5 h-5"}),(0,d.jsx)("span",{children:"Add Balance"})]}),(0,d.jsx)(f.BT,{children:"Top up your credits or learn how to earn more points"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)("div",{className:"flex flex-wrap gap-3",children:(0,d.jsxs)(g.$,{className:"flex items-center space-x-2",children:[(0,d.jsx)(q.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Top Up Credits"})]})})})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsxs)(f.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(w,{className:"w-5 h-5"}),(0,d.jsx)("span",{children:"Recent Transactions"})]}),(0,d.jsx)(f.BT,{children:"Latest point and credit transactions"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(k,{defaultValue:"points",className:"w-full",children:[(0,d.jsxs)(l,{className:"grid w-full grid-cols-2",children:[(0,d.jsxs)(m,{value:"points",className:"flex items-center space-x-2",children:[(0,d.jsx)(p.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Points"})]}),(0,d.jsxs)(m,{value:"credits",className:"flex items-center space-x-2",children:[(0,d.jsx)(q.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Credits"})]})]}),(0,d.jsx)(n,{value:"points",className:"space-y-4",children:s.length>0?(0,d.jsx)("div",{className:"space-y-3",children:s.map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[H(a.transaction_type),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:a.description}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:new Date(a.created_at).toLocaleDateString("id-ID")})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("p",{className:`font-semibold ${G(a.transaction_type)}`,children:["USED"===a.transaction_type?"-":"+",a.formattedAmount]}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:["Balance: ",F(a.balance_after)]})]})]},a.id))}):(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)(p.A,{className:"w-12 h-12 mx-auto mb-4 text-gray-300"}),(0,d.jsx)("p",{children:"No point transactions yet"})]})}),(0,d.jsx)(n,{value:"credits",className:"space-y-4",children:y.length>0?(0,d.jsx)("div",{className:"space-y-3",children:y.map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[H(a.transaction_type),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:a.description}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:new Date(a.created_at).toLocaleDateString("id-ID")})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("p",{className:`font-semibold ${G(a.transaction_type)}`,children:["USED"===a.transaction_type?"-":"+",a.formattedAmount]}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:["Balance: ",F(a.balance_after)]})]})]},a.id))}):(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)(q.A,{className:"w-12 h-12 mx-auto mb-4 text-gray-300"}),(0,d.jsx)("p",{children:"No credit transactions yet"})]})})]})})]})]})}function y(){return(0,d.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,d.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Balance Management"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Manage your points and credits for AI Trainer Hub services"})]}),(0,d.jsx)(x,{})]})})}},95215:(a,b,c)=>{Promise.resolve().then(c.bind(c,94762))},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,842,928,263],()=>b(b.s=33331));module.exports=c})();