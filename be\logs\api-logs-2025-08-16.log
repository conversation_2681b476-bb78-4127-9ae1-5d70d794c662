2025-08-16T09:09:38.119Z - {
  "requestId": "s0e7bshou",
  "timestamp": "2025-08-16T09:09:38.118Z",
  "duration": "13ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/public",
    "path": "/public",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 500,
    "headers": {},
    "body": {
      "success": false,
      "error": "Failed to get public experts",
      "message": "connect ECONNREFUSED 127.0.0.1:3306"
    },
    "size": "104 bytes"
  }
}
2025-08-16T09:09:38.124Z - {
  "requestId": "s0e7bshou",
  "timestamp": "2025-08-16T09:09:38.124Z",
  "duration": "19ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/public",
    "path": "/public",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 500,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "success": false,
      "error": "Failed to get public experts",
      "message": "connect ECONNREFUSED 127.0.0.1:3306"
    },
    "size": "104 bytes"
  }
}
2025-08-16T09:10:08.767Z - {
  "requestId": "yi4wenwbl",
  "timestamp": "2025-08-16T09:10:08.767Z",
  "duration": "5ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Login failed",
      "message": "connect ECONNREFUSED 127.0.0.1:3306"
    },
    "size": "72 bytes"
  }
}
2025-08-16T09:10:08.768Z - {
  "requestId": "yi4wenwbl",
  "timestamp": "2025-08-16T09:10:08.768Z",
  "duration": "6ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Login failed",
      "message": "connect ECONNREFUSED 127.0.0.1:3306"
    },
    "size": "72 bytes"
  }
}
2025-08-16T09:19:10.599Z - {
  "requestId": "zi4vnpsez",
  "timestamp": "2025-08-16T09:19:10.598Z",
  "duration": "767ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "8df6d705-9df8-4d1a-84e0-8c391f834717"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:19:10.600Z - {
  "requestId": "zi4vnpsez",
  "timestamp": "2025-08-16T09:19:10.600Z",
  "duration": "769ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "8df6d705-9df8-4d1a-84e0-8c391f834717"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:19:17.771Z - {
  "requestId": "g7nmz0zwn",
  "timestamp": "2025-08-16T09:19:17.771Z",
  "duration": "187ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "c7194dc3-58e3-429c-b08f-45d42da9b58c"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:19:17.772Z - {
  "requestId": "g7nmz0zwn",
  "timestamp": "2025-08-16T09:19:17.772Z",
  "duration": "188ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "c7194dc3-58e3-429c-b08f-45d42da9b58c"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:19:39.369Z - {
  "requestId": "hilmk2bie",
  "timestamp": "2025-08-16T09:19:39.369Z",
  "duration": "11ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer c7194dc3-58e3-429c-b08f-45d42da9b58c"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:19:39.370Z - {
  "requestId": "hilmk2bie",
  "timestamp": "2025-08-16T09:19:39.370Z",
  "duration": "12ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer c7194dc3-58e3-429c-b08f-45d42da9b58c"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:22:03.478Z - {
  "requestId": "o3q7wtqp5",
  "timestamp": "2025-08-16T09:22:03.477Z",
  "duration": "46ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/public",
    "path": "/public",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "message": "[RESPONSE TOO LARGE - TRUNCATED]",
      "size": 12639,
      "preview": "{\"success\":true,\"experts\":[{\"id\":65,\"name\":\"english tutor\",\"description\":\"english tutor desc\",\"systemPrompt\":\"Anda adalah **English Coach**, tutor AI yang antusias, sabar, dan suportif, khusus untuk orang dewasa Indonesia (18 +). Semua modul pelajaran (Module 1, Module 2, dst.) sudah tersedia di sistem. Gunakan Bahasa Indonesia dalam penjelasan dan tanya-jawab, sambil menyisipkan materi Bahasa Inggris yang sedang dipelajari. Terapkan metode Soskratik: hanya satu pertanyaan per giliran, tunggu ja..."
    },
    "size": "12784 bytes"
  }
}
2025-08-16T09:22:03.480Z - {
  "requestId": "o3q7wtqp5",
  "timestamp": "2025-08-16T09:22:03.479Z",
  "duration": "48ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/public",
    "path": "/public",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "message": "[RESPONSE TOO LARGE - TRUNCATED]",
      "size": 12639,
      "preview": "{\"success\":true,\"experts\":[{\"id\":65,\"name\":\"english tutor\",\"description\":\"english tutor desc\",\"systemPrompt\":\"Anda adalah **English Coach**, tutor AI yang antusias, sabar, dan suportif, khusus untuk orang dewasa Indonesia (18 +). Semua modul pelajaran (Module 1, Module 2, dst.) sudah tersedia di sistem. Gunakan Bahasa Indonesia dalam penjelasan dan tanya-jawab, sambil menyisipkan materi Bahasa Inggris yang sedang dipelajari. Terapkan metode Soskratik: hanya satu pertanyaan per giliran, tunggu ja..."
    },
    "size": "12784 bytes"
  }
}
2025-08-16T09:22:03.499Z - {
  "requestId": "y841fwu9g",
  "timestamp": "2025-08-16T09:22:03.499Z",
  "duration": "17ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer c7194dc3-58e3-429c-b08f-45d42da9b58c"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:22:03.500Z - {
  "requestId": "y841fwu9g",
  "timestamp": "2025-08-16T09:22:03.500Z",
  "duration": "18ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer c7194dc3-58e3-429c-b08f-45d42da9b58c"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:26:24.937Z - {
  "requestId": "xpxmg8iwu",
  "timestamp": "2025-08-16T09:26:24.936Z",
  "duration": "287ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "9e28ddd4-e8eb-48c4-a45e-b2843909ced0"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:26:24.939Z - {
  "requestId": "xpxmg8iwu",
  "timestamp": "2025-08-16T09:26:24.938Z",
  "duration": "289ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "9e28ddd4-e8eb-48c4-a45e-b2843909ced0"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:26:32.181Z - {
  "requestId": "3wrdv31fc",
  "timestamp": "2025-08-16T09:26:32.181Z",
  "duration": "8ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer 9e28ddd4-e8eb-48c4-a45e-b2843909ced0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:26:32.181Z - {
  "requestId": "3wrdv31fc",
  "timestamp": "2025-08-16T09:26:32.181Z",
  "duration": "8ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer 9e28ddd4-e8eb-48c4-a45e-b2843909ced0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
