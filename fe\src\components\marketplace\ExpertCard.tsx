"use client";

import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Expert } from "@/types/filters";
import { cn } from "@/lib/utils";
import { highlightAndTruncate, highlightSearchTerm } from "@/utils/searchHighlight";
import { Star, MessageCircle, DollarSign } from "lucide-react";

interface ExpertCardProps {
  expert: Expert;
  onChatClick?: (expertId: number) => void;
  className?: string;
  showStats?: boolean;
  searchTerm?: string;
}

export const ExpertCard: React.FC<ExpertCardProps> = ({
  expert,
  onChatClick,
  className = "",
  showStats = true,
  searchTerm = ""
}) => {
  const handleChatClick = () => {
    if (onChatClick) {
      onChatClick(expert.id);
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <Card
      className={cn(
        "group hover:shadow-lg transition-all duration-300 border border-gray-200 hover:border-gray-300 bg-white",
        "hover:scale-[1.02] cursor-pointer",
        className
      )}
      onClick={handleChatClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start gap-3">
          <Avatar className="h-12 w-12 border-2 border-gray-100">
            <AvatarImage
              src={expert.imageUrl}
              alt={expert.name}
              className="object-cover"
            />
            <AvatarFallback className="bg-primary/10 text-primary font-semibold">
              {expert.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 text-lg leading-tight truncate">
              {searchTerm ? highlightSearchTerm(expert.name, searchTerm, "bg-yellow-200 text-yellow-900 px-0.5 rounded") : expert.name}
            </h3>

            {expert.averageRating > 0 && (
              <div className="flex items-center gap-1 mt-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm font-medium text-gray-700">
                  {expert.averageRating.toFixed(1)}
                </span>
                <span className="text-sm text-gray-500">
                  ({expert.totalReviews} reviews)
                </span>
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3">
          {searchTerm ? highlightAndTruncate(expert.description, searchTerm, 120, "bg-yellow-200 text-yellow-900 px-0.5 rounded") : expert.description}
        </div>

        {/* Labels */}
        {expert.labels && expert.labels.length > 0 && (
          <div className="flex flex-wrap gap-1.5 mb-4">
            {expert.labels.slice(0, 3).map((label, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="text-xs px-2 py-1 bg-gray-100 text-gray-700 hover:bg-gray-200"
              >
                {label}
              </Badge>
            ))}
            {expert.labels.length > 3 && (
              <Badge
                variant="outline"
                className="text-xs px-2 py-1 text-gray-500"
              >
                +{expert.labels.length - 3} more
              </Badge>
            )}
          </div>
        )}

        {/* Stats */}
        {showStats && (
          <div className="grid grid-cols-2 gap-3 mb-4 text-sm">
            <div className="flex items-center gap-2 text-gray-600">
              <MessageCircle className="h-4 w-4" />
              <span>{formatNumber(expert.totalChats)} chats</span>
            </div>

            {expert.totalRevenue > 0 && (
              <div className="flex items-center gap-2 text-gray-600">
                <DollarSign className="h-4 w-4" />
                <span>{formatCurrency(expert.totalRevenue)}</span>
              </div>
            )}
          </div>
        )}

        {/* Action Button */}
        <Button
          className="w-full group-hover:bg-primary group-hover:text-white transition-colors duration-200"
          variant="outline"
          onClick={(e) => {
            e.stopPropagation();
            handleChatClick();
          }}
        >
          Start Chat
        </Button>
      </CardContent>
    </Card>
  );
};