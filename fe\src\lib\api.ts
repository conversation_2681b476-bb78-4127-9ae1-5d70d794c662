import axios, { AxiosRequestConfig, AxiosResponse } from "axios";

const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001";

interface ApiOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE";
  body?: any;
  headers?: Record<string, string>;
  skipAuth?: boolean; // Skip authentication for public endpoints
}

// User registration/authentication types
export interface RegisterData {
  name: string;
  email: string;
  phone: string;
  password: string;
  referralCode?: string;
}

export interface LoginData {
  phone: string;
  password: string;
}

export interface VerifyOTPData {
  phone: string;
  code: string;
}

export interface UpdateProfileData {
  name: string;
  email: string;
  bank_name?: string;
  account_holder_name?: string;
  account_number?: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export interface User {
  user_id: number;
  phone: string;
  name: string;
  email: string;
  bank_name?: string;
  account_holder_name?: string;
  account_number?: string;
  token?: string;
}

// Create axios instance with default configuration
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
  timeout: 30000, // 30 seconds timeout
});

// Helper function to get authentication token
function getAuthToken(): string | null {
  if (typeof window !== "undefined") {
    return localStorage.getItem("token");
  }
  return null;
}

// Request interceptor to add auth token
axiosInstance.interceptors.request.use(
  (config: any) => {
    const authToken = getAuthToken();

    // Debug logging
    console.log("🔍 API Call Debug:", {
      endpoint: config.url,
      fullUrl: `${API_URL}${config.url}`,
      API_URL,
      hasToken: !!authToken,
      tokenPreview: authToken ? authToken.substring(0, 3) + "***" : "none",
      method: config.method?.toUpperCase(),
      data: config.data,
      "process.env.NEXT_PUBLIC_API_URL": process.env.NEXT_PUBLIC_API_URL,
    });

    // Add auth token if available and not skipped
    if (authToken && !config.metadata?.skipAuth) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    return config;
  },
  (error) => {
    console.error("💥 Request interceptor error:", error);
    return Promise.reject(error);
  }
);

// Response interceptor for logging and error handling
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log("📡 Response status:", response.status, response.statusText);
    console.log("✅ API Success:", response.data);
    return response;
  },
  (error) => {
    console.error("❌ API Error:", error.response?.data || error.message);
    console.error("💥 API call failed:", error);

    // Extract error message
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      `HTTP error! status: ${error.response?.status}`;

    throw new Error(errorMessage);
  }
);

interface ApiCallConfig extends AxiosRequestConfig {
  metadata: any;
}

export async function apiCall(endpoint: string, options: ApiOptions = {}) {
  const { method = "GET", body, headers = {}, skipAuth = false } = options;

  const config: ApiCallConfig = {
    method: method.toLowerCase() as any,
    url: endpoint,
    headers: {
      ...headers,
    },
    metadata: { skipAuth } as any, // Custom metadata for interceptor
  };

  // Add data for non-GET requests
  if (body && method !== "GET") {
    config.data = body;
  }

  try {
    const response = await axiosInstance(config);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// Specific API functions
export const api = {
  // Generic HTTP methods
  get: (endpoint: string, options: ApiOptions = {}) => 
    apiCall(endpoint, { ...options, method: "GET" }),
  
  post: (endpoint: string, options: ApiOptions = {}) => 
    apiCall(endpoint, { ...options, method: "POST" }),
  
  put: (endpoint: string, options: ApiOptions = {}) => 
    apiCall(endpoint, { ...options, method: "PUT" }),
  
  delete: (endpoint: string, options: ApiOptions = {}) => 
    apiCall(endpoint, { ...options, method: "DELETE" }),

  // Health check (no token required)
  health: () => apiCall("/health"),

  // Chat endpoints (token required)
  chat: (
    message: string,
    threadId?: string,
    expertId?: string,
    expertContext?: any
  ) =>
    apiCall("/api/chat", {
      method: "POST",
      body: { message, threadId, expertId, expertContext },
    }),

  getThreadMessages: (threadId: string) =>
    apiCall(`/api/thread/${threadId}/messages`),

  getSessionMessages: (sessionId: number, limit?: number) =>
    apiCall(
      `/api/chat/sessions/${sessionId}/messages${limit ? `?limit=${limit}` : ""
      }`
    ),

  // Chat session endpoints
  getUserChatSessions: (limit?: number) =>
    apiCall(`/api/chat/sessions${limit ? `?limit=${limit}` : ""}`),

  getUserStats: () => apiCall("/api/chat/stats"),

  getActiveSessionForExpert: (expertId: string) =>
    apiCall(`/api/chat/sessions/expert/${expertId}`),

  updateSessionTitle: (sessionId: string, title: string) =>
    apiCall(`/api/chat/sessions/${sessionId}/title`, {
      method: "PUT",
      body: { title },
    }),

  deleteSession: (sessionId: string) =>
    apiCall(`/api/chat/sessions/${sessionId}`, {
      method: "DELETE",
    }),



  // Assistant endpoints (token required)
  createThread: () => apiCall("/assistant/thread", { method: "POST" }),

  sendMessage: (threadId: string, message: string) =>
    apiCall("/assistant/message", {
      method: "POST",
      body: { threadId, message },
    }),

  runAssistant: (threadId: string) =>
    apiCall("/assistant/run", {
      method: "POST",
      body: { threadId },
    }),

  getMessages: (threadId: string) => apiCall(`/assistant/messages/${threadId}`),

  // Expert endpoints (token required)
  createExpert: async (expertData: FormData) => {
    const authToken = getAuthToken();

    try {
      const response = await axios.post(`${API_URL}/api/experts`, expertData, {
        headers: {
          "Content-Type": "multipart/form-data",
          ...(authToken ? { Authorization: `Bearer ${authToken}` } : {}),
        },
      });
      return response.data;
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        `HTTP error! status: ${error.response?.status}`;
      throw new Error(errorMessage);
    }
  },

  listExperts: () => apiCall("/api/experts"),

  // Get public experts (no authentication required)
  getPublicExperts: () => apiCall("/api/experts/public", { skipAuth: true }),

  getExpert: (expertId: string) => apiCall(`/api/experts/${expertId}`),

  updateExpert: async (
    expertId: number,
    expertData: any,
    knowledgeBaseFile?: File | null,
    imageFile?: File | null
  ) => {
    const authToken = getAuthToken();
    const formData = new FormData();

    // Add text fields
    Object.keys(expertData).forEach((key) => {
      if (expertData[key] !== undefined && expertData[key] !== null) {
        if (key === "labels" && Array.isArray(expertData[key])) {
          formData.append(key, JSON.stringify(expertData[key]));
        } else {
          formData.append(key, expertData[key].toString());
        }
      }
    });

    // Add files
    if (knowledgeBaseFile) {
      formData.append("file", knowledgeBaseFile);
    }
    if (imageFile) {
      formData.append("image", imageFile);
    }

    try {
      const response = await axios.put(
        `${API_URL}/api/experts/${expertId}`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            ...(authToken ? { Authorization: `Bearer ${authToken}` } : {}),
          },
        }
      );
      return response.data;
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        `HTTP error! status: ${error.response?.status}`;
      throw new Error(errorMessage);
    }
  },

  // Model endpoints
  getAvailableModels: () => apiCall("/api/models"),

  getModelPricing: (model: string) => apiCall(`/api/models/${model}/pricing`),

  calculateCost: (
    model: string,
    inputTokens: number,
    outputTokens: number,
    pricingPercentage: number
  ) =>
    apiCall("/api/calculate-cost", {
      method: "POST",
      body: { model, inputTokens, outputTokens, pricingPercentage },
    }),

  // Expert statistics endpoints
  getExpertStats: (expertId: string) =>
    apiCall(`/api/experts/${expertId}/stats`),

  // Review endpoints (added directly to avoid type issues)
  createReview: (reviewData: {
    expertId: number;
    rating: number;
    reviewText?: string;
  }) =>
    apiCall("/api/reviews", {
      method: "POST",
      body: reviewData,
    }),

  updateReview: (reviewId: number, reviewData: {
    rating: number;
    reviewText?: string;
  }) =>
    apiCall(`/api/reviews/${reviewId}`, {
      method: "PUT",
      body: reviewData,
    }),

  getExpertReviews: (expertId: number, page: number = 1, limit: number = 10) =>
    apiCall(`/api/reviews/expert/${expertId}?page=${page}&limit=${limit}`, {
      skipAuth: true,
    }),

  getUserReviews: (page: number = 1, limit: number = 10) =>
    apiCall(`/api/reviews/my?page=${page}&limit=${limit}`),

  getReview: (reviewId: number) =>
    apiCall(`/api/reviews/${reviewId}`, {
      skipAuth: true,
    }),

  canUserReview: (expertId: number) =>
    apiCall(`/api/reviews/expert/${expertId}/can-review`),

  getExpertRatingStats: (expertId: number) =>
    apiCall(`/api/reviews/expert/${expertId}/stats`, {
      skipAuth: true,
    }),
};

// Authentication API
export const authAPI = {
  // Register new user
  register: (userData: RegisterData) =>
    apiCall("/api/users/register", {
      method: "POST",
      body: userData,
      skipAuth: true,
    }),

  // Verify OTP
  verifyOTP: (data: VerifyOTPData) =>
    apiCall("/api/users/verify-otp", {
      method: "POST",
      body: data,
      skipAuth: true,
    }),

  // Login user
  login: (credentials: LoginData) =>
    apiCall("/api/users/login", {
      method: "POST",
      body: credentials,
      skipAuth: true,
    }),

  // Get current user profile
  getProfile: () => apiCall("/api/users/profile"),

  // Update user profile
  updateProfile: (profileData: UpdateProfileData) =>
    apiCall("/api/users/profile", {
      method: "PUT",
      body: profileData,
    }),

  // Change password
  changePassword: (passwordData: ChangePasswordData) =>
    apiCall("/api/users/change-password", {
      method: "POST",
      body: passwordData,
    }),

  // Resend OTP
  resendOTP: (phone: string) =>
    apiCall("/api/users/resend-otp", {
      method: "POST",
      body: { phone },
      skipAuth: true,
    }),

  // Forgot password
  forgotPassword: (phone: string) =>
    apiCall("/api/users/forgot-password", {
      method: "POST",
      body: { phone },
      skipAuth: true,
    }),

  // Reset password
  resetPassword: (phone: string, code: string, newPassword: string) =>
    apiCall("/api/users/reset-password", {
      method: "POST",
      body: { phone, code, newPassword },
      skipAuth: true,
    }),

  // Logout
  logout: () =>
    apiCall("/api/users/logout", {
      method: "POST",
    }),

  // Balance endpoints
  getBalanceSummary: () => apiCall("/api/balance/summary"),

  getPointTransactions: (limit?: number) =>
    apiCall(
      `/api/balance/transactions/points${limit ? `?limit=${limit}` : ""}`
    ),

  getCreditTransactions: (limit?: number) =>
    apiCall(
      `/api/balance/transactions/credits${limit ? `?limit=${limit}` : ""}`
    ),

  checkAffordability: (amount: number) =>
    apiCall("/api/balance/can-afford", {
      method: "POST",
      body: { amount },
    }),

  addPoints: (amount: number, description: string) =>
    apiCall("/api/balance/points/add", {
      method: "POST",
      body: { amount, description },
    }),

  addCredits: (amount: number, description: string) =>
    apiCall("/api/balance/credits/add", {
      method: "POST",
      body: { amount, description },
    }),
};

// Review API functions
export const reviewAPI = {
  // Create a new review
  createReview: (reviewData: {
    expertId: number;
    rating: number;
    reviewText?: string;
  }) =>
    apiCall("/api/reviews", {
      method: "POST",
      body: reviewData,
    }),

  // Update an existing review
  updateReview: (reviewId: number, reviewData: {
    rating: number;
    reviewText?: string;
  }) =>
    apiCall(`/api/reviews/${reviewId}`, {
      method: "PUT",
      body: reviewData,
    }),

  // Get reviews for an expert
  getExpertReviews: (expertId: number, page: number = 1, limit: number = 10) =>
    apiCall(`/api/reviews/expert/${expertId}?page=${page}&limit=${limit}`, {
      skipAuth: true,
    }),

  // Get current user's reviews
  getUserReviews: (page: number = 1, limit: number = 10) =>
    apiCall(`/api/reviews/my?page=${page}&limit=${limit}`),

  // Get review by ID
  getReview: (reviewId: number) =>
    apiCall(`/api/reviews/${reviewId}`, {
      skipAuth: true,
    }),

  // Check if user can review an expert
  canUserReview: (expertId: number) =>
    apiCall(`/api/reviews/expert/${expertId}/can-review`),

  // Get expert rating statistics
  getExpertRatingStats: (expertId: number) =>
    apiCall(`/api/reviews/expert/${expertId}/stats`, {
      skipAuth: true,
    }),

  // Admin functions
  hideReview: (reviewId: number) =>
    apiCall(`/api/reviews/admin/${reviewId}/hide`, {
      method: "PUT",
    }),

  showReview: (reviewId: number) =>
    apiCall(`/api/reviews/admin/${reviewId}/show`, {
      method: "PUT",
    }),

  deleteReview: (reviewId: number) =>
    apiCall(`/api/reviews/admin/${reviewId}`, {
      method: "DELETE",
    }),

  getPendingReviews: (page: number = 1, limit: number = 20) =>
    apiCall(`/api/reviews/admin/pending?page=${page}&limit=${limit}`),

  // Expert filtering functions
  getFilteredExperts: (params: {
    filter?: string;
    timeline?: string;
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    const queryParams = new URLSearchParams();

    if (params.filter) queryParams.append('filter', params.filter);
    if (params.timeline) queryParams.append('timeline', params.timeline);
    if (params.search) queryParams.append('search', params.search);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());

    return apiCall(`/api/experts/filtered?${queryParams.toString()}`, { skipAuth: true });
  },

  getFilterAnalytics: (days: number = 30) =>
    apiCall(`/api/experts/filter-analytics?days=${days}`),
};

// Review functions are now directly included in the api object above

export { axiosInstance };
