(()=>{var a={};a.id=457,a.ids=[457],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(a,b,c)=>{"use strict";c.d(b,{L:()=>h,cn:()=>g});var d=c(49384),e=c(82348);let f=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001";function g(...a){return(0,e.QP)((0,d.$)(a))}function h(a){return`${f}/${a.startsWith("/")?a.replace("/",""):a}`}},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8973:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\Web\\\\pakarai\\\\ai-trainer\\\\fe\\\\src\\\\app\\\\chat\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\chat\\page.tsx","default")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},22826:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>E});var d=c(60687),e=c(43210),f=c(16189),g=c(85814),h=c.n(g),i=c(28559);c(62185);var j=c(30474),k=c(62688);let l=(0,k.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var m=c(89667),n=c(29523),o=c(44493),p=c(41862),q=c(58869),r=c(4780);let s=({role:a,content:b,isStreaming:c=!1,timestamp:f,cost:g,tokens:h,expertName:i,expertImageUrl:k,expertIcon:l="\uD83E\uDD16"})=>{let m=(0,e.useRef)(null);return(0,e.useEffect)(()=>{c&&m.current&&m.current.scrollIntoView({behavior:"smooth",block:"nearest"})},[b,c]),(0,d.jsxs)("div",{ref:m,className:`flex gap-3 ${"user"===a?"justify-end":"justify-start"} group`,children:["assistant"===a&&(0,d.jsx)("div",{className:"flex-shrink-0",children:k?(0,d.jsx)(j.default,{src:(0,r.L)(k),alt:i||"AI Assistant",width:32,height:32,className:"w-8 h-8 object-cover rounded-full border border-gray-200"}):(0,d.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm",style:{backgroundColor:"#1E3A8A"},children:l})}),(0,d.jsxs)("div",{className:"flex flex-col max-w-[70%]",children:[(0,d.jsxs)("div",{className:`rounded-2xl px-4 py-3 ${"user"===a?"text-white shadow-lg":"bg-gray-50 text-gray-900 border border-gray-100"} ${c?"animate-pulse":""}`,style:"user"===a?{backgroundColor:"#1E3A8A"}:{},children:[(0,d.jsx)("div",{className:"text-sm leading-relaxed",dangerouslySetInnerHTML:{__html:b.replace(/\n/g,"<br/>")}}),c&&(0,d.jsxs)("div",{className:"flex items-center space-x-2 mt-2 pt-2 border-t border-gray-200",children:[(0,d.jsx)(p.A,{className:"w-3 h-3 animate-spin text-gray-500"}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"Streaming..."})]})]}),(0,d.jsxs)("div",{className:`flex items-center space-x-2 mt-1 px-2 ${"user"===a?"justify-end":"justify-start"}`,children:[(0,d.jsx)("span",{className:"text-xs text-gray-400",children:new Date(f).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})}),"assistant"===a&&!c&&(g||h)&&(0,d.jsxs)(d.Fragment,{children:[h&&(0,d.jsxs)("span",{className:"text-xs text-gray-400",children:["• ",h.toLocaleString()," tokens"]}),g&&(0,d.jsxs)("span",{className:"text-xs text-gray-400",children:["• ",new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0,maximumFractionDigits:0}).format(g)]})]})]})]}),"user"===a&&(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,d.jsx)(q.A,{className:"w-4 h-4 text-gray-600"})})})]})},t=({expertName:a,expertImageUrl:b,expertIcon:c="\uD83E\uDD16"})=>(0,d.jsxs)("div",{className:"flex gap-3 justify-start",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:b?(0,d.jsx)(j.default,{src:(0,r.L)(b),alt:a||"AI Assistant",width:32,height:32,className:"w-8 h-8 object-cover rounded-full border border-gray-200"}):(0,d.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm",style:{backgroundColor:"#1E3A8A"},children:c})}),(0,d.jsxs)("div",{className:"rounded-2xl px-4 py-3 bg-gray-50 border border-gray-100 flex items-center gap-2",children:[(0,d.jsxs)("div",{className:"flex space-x-1",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,d.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,d.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:[a||"AI"," is thinking..."]})]})]});var u=c(35583),v=c(25541),w=c(93613);let x=({currentCost:a,totalTokens:b,balanceUpdate:c,isStreaming:f,className:g=""})=>{let[h,i]=(0,e.useState)(0),[j,k]=(0,e.useState)(0);(0,e.useEffect)(()=>{if(a>h){let b=(a-h)/10,c=setInterval(()=>{i(d=>{let e=d+b;return e>=a?(clearInterval(c),a):e})},50);return()=>clearInterval(c)}i(a)},[a,h]),(0,e.useEffect)(()=>{if(b>j){let a=(b-j)/10,c=setInterval(()=>{k(d=>{let e=d+a;return e>=b?(clearInterval(c),b):e})},50);return()=>clearInterval(c)}k(b)},[b,j]);let l=a=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0,maximumFractionDigits:0}).format(a);return(0,d.jsxs)("div",{className:`bg-white border border-gray-200 rounded-lg p-4 shadow-sm ${g}`,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(u.A,{className:"w-4 h-4 text-blue-600"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:f?"Live Cost":"Session Cost"})]}),f&&(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,d.jsx)("span",{className:"text-xs text-green-600",children:"Live"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Current Cost:"}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[f&&h>0&&(0,d.jsx)(v.A,{className:"w-3 h-3 text-orange-500"}),(0,d.jsx)("span",{className:`text-sm font-semibold ${f?"text-orange-600":"text-gray-900"}`,children:l(h)})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Tokens Used:"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:Math.round(j).toLocaleString()})]}),c&&(0,d.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,d.jsxs)("div",{className:"space-y-1",children:[c.pointsUsed>0&&(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"Points Used:"}),(0,d.jsxs)("span",{className:"text-xs font-medium text-red-600",children:["-",c.pointsUsed.toLocaleString()]})]}),c.creditsUsed>0&&(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"Credits Used:"}),(0,d.jsxs)("span",{className:"text-xs font-medium text-red-600",children:["-",l(c.creditsUsed)]})]}),c.generatesCommission&&(0,d.jsxs)("div",{className:"flex items-center space-x-1 mt-1",children:[(0,d.jsx)(v.A,{className:"w-3 h-3 text-green-500"}),(0,d.jsx)("span",{className:"text-xs text-green-600",children:"Generates commission"})]})]})}),h>5e4&&(0,d.jsxs)("div",{className:"flex items-center space-x-1 pt-2 border-t border-orange-100",children:[(0,d.jsx)(w.A,{className:"w-3 h-3 text-orange-500"}),(0,d.jsx)("span",{className:"text-xs text-orange-600",children:"High usage detected"})]})]}),f&&(0,d.jsx)("div",{className:"mt-3 pt-2 border-t border-gray-100",children:(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,d.jsxs)("div",{className:"flex space-x-1",children:[(0,d.jsx)("div",{className:"w-1 h-1 bg-blue-500 rounded-full animate-bounce"}),(0,d.jsx)("div",{className:"w-1 h-1 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,d.jsx)("div",{className:"w-1 h-1 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,d.jsx)("span",{className:"text-xs text-blue-600",children:"Processing..."})]})})]})};var y=c(5336);let z=(0,k.A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),A=({isConnected:a,connectionError:b,onReconnect:c,className:e=""})=>{let f=(()=>{if(b){let a=b.includes("log in")||b.includes("Authentication");return{icon:w.A,text:a?"Authentication Required":"Connection Error",color:a?"text-yellow-600":"text-red-600",bgColor:a?"bg-yellow-50":"bg-red-50",borderColor:a?"border-yellow-200":"border-red-200",detail:b}}return a?{icon:y.A,text:"Connected",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200",detail:"Real-time chat enabled"}:{icon:z,text:"Connecting...",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200",detail:"Establishing connection"}})(),g=f.icon;return(0,d.jsxs)("div",{className:`flex items-center space-x-2 px-3 py-2 rounded-lg border ${f.bgColor} ${f.borderColor} ${e}`,children:[(0,d.jsx)(g,{className:`w-4 h-4 ${f.color}`}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("div",{className:`text-sm font-medium ${f.color}`,children:f.text}),(0,d.jsx)("div",{className:"text-xs text-gray-500 truncate",children:f.detail})]}),!a&&!b&&(0,d.jsxs)("div",{className:"flex space-x-1",children:[(0,d.jsx)("div",{className:"w-1 h-1 bg-yellow-500 rounded-full animate-bounce"}),(0,d.jsx)("div",{className:"w-1 h-1 bg-yellow-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,d.jsx)("div",{className:"w-1 h-1 bg-yellow-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),b&&c&&(0,d.jsx)("button",{onClick:c,className:"text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors",children:"Retry"})]})};var B=c(29614);let C=({expert:a,sessionId:b,initialMessages:c=[]})=>{let[f,g]=(0,e.useState)(""),k=(0,e.useRef)(null),{isConnected:p,connectionError:q,reconnect:u}=(0,B.F)(),{messages:v,isStreaming:w,isTyping:y,error:z,currentCost:C,totalTokens:D,balanceUpdate:E,sendMessage:F,clearError:G,addMessage:H}=(a=>{let{socket:b,isConnected:c,joinChat:d,leaveChat:f,sendChatMessage:g}=(0,B.F)(),{expertId:h,sessionId:i,onMessageComplete:j,onCostUpdate:k,onBalanceUpdate:l,onError:m}=a,n=h&&""!==h.trim(),[o,p]=(0,e.useState)({messages:[],isStreaming:!1,isTyping:!1,currentStreamId:null,connectionStatus:"disconnected",error:null,currentCost:0,totalTokens:0,balanceUpdate:null}),q=(0,e.useRef)(null),r=(0,e.useRef)(0),s=()=>(r.current+=1,`msg_${Date.now()}_${r.current}`);(0,e.useEffect)(()=>{p(a=>({...a,connectionStatus:c?"connected":"disconnected"}))},[c]),(0,e.useEffect)(()=>(c&&n&&(console.log("\uD83C\uDFE0 Auto-joining chat for expert:",h),d(h,i)),()=>{c&&f()}),[c,n,h,i,d,f]),(0,e.useEffect)(()=>{if(!b)return;let a=a=>{console.log("\uD83D\uDE80 Stream started:",a),p(b=>({...b,isStreaming:!0,currentStreamId:a.streamId,error:null})),q.current={id:s(),role:"assistant",content:"",timestamp:Date.now(),isStreaming:!0},p(a=>({...a,messages:[...a.messages,q.current]}))},c=a=>{console.log("\uD83D\uDCE6 Stream chunk:",a),q.current&&a.streamId===o.currentStreamId&&a.content&&(q.current.content+=a.content,p(a=>({...a,messages:a.messages.map(a=>a&&a.id===q.current?.id?{...q.current}:a).filter(a=>null!==a)})))},d=a=>{console.log("\uD83D\uDCB0 Cost update:",a),p(b=>({...b,currentCost:a.estimatedCost,totalTokens:a.totalTokens})),k&&k(a.estimatedCost,a.totalTokens)},e=a=>{console.log("✅ Stream complete:",a),q.current&&(q.current.isStreaming=!1,q.current.cost=a.cost,q.current.tokens=a.totalTokens,p(b=>({...b,messages:b.messages.map(a=>a&&a.id===q.current?.id?{...q.current}:a).filter(a=>null!==a),isStreaming:!1,currentStreamId:null,currentCost:a.cost,totalTokens:a.totalTokens,balanceUpdate:a.balanceUsage})),j&&j(q.current),l&&a.balanceUsage&&l(a.balanceUsage),q.current=null)},f=a=>{console.error("❌ Stream error:",a),p(b=>({...b,isStreaming:!1,currentStreamId:null,error:a.error})),m&&m(a.error),q.current&&(p(a=>({...a,messages:a.messages.filter(a=>a&&a.id!==q.current?.id)})),q.current=null)},g=a=>{console.log("⌨️ Typing started:",a),p(a=>({...a,isTyping:!0}))},h=a=>{console.log("⌨️ Typing stopped:",a),p(a=>({...a,isTyping:!1}))};return b.on("stream_started",a),b.on("stream_chunk",c),b.on("cost_update",d),b.on("stream_complete",e),b.on("stream_error",f),b.on("typing_start",g),b.on("typing_stop",h),()=>{b.off("stream_started",a),b.off("stream_chunk",c),b.off("cost_update",d),b.off("stream_complete",e),b.off("stream_error",f),b.off("typing_start",g),b.off("typing_stop",h)}},[b,o.currentStreamId,j,k,l,m]);let t=(0,e.useCallback)(a=>{if(!a.trim()||o.isStreaming)return;if(!n){console.error("❌ Cannot send message: expertId is required"),p(a=>({...a,error:"Expert not loaded. Please wait and try again."}));return}let b={id:s(),role:"user",content:a.trim(),timestamp:Date.now()};p(a=>({...a,messages:[...a.messages,b],error:null})),console.log("\uD83D\uDCE4 Sending message via socket:",{expertId:h,message:a.substring(0,50)+"...",sessionId:i}),g(a.trim(),h,i)},[n,h,i,g,o.isStreaming]),u=(0,e.useCallback)(()=>{p(a=>({...a,error:null}))},[]),v=(0,e.useCallback)(a=>{if(!a||!a.content)return void console.warn("⚠️ Attempted to add null or empty message:",a);let b={...a,id:s(),content:a.content||""};p(a=>({...a,messages:[...a.messages.filter(a=>null!==a),b]}))},[]),w=(0,e.useCallback)(()=>{p(a=>({...a,messages:[],currentCost:0,totalTokens:0,balanceUpdate:null}))},[]);return{...o,sendMessage:t,clearError:u,addMessage:v,clearMessages:w}})({expertId:a?.id.toString()||"",sessionId:b,onMessageComplete:a=>{console.log("✅ Message completed:",a)},onCostUpdate:(a,b)=>{console.log("\uD83D\uDCB0 Cost updated:",{cost:a,tokens:b})},onBalanceUpdate:a=>{console.log("\uD83D\uDCB3 Balance updated:",a)},onError:a=>{console.error("❌ Chat error:",a)}}),I=(0,e.useRef)(!1);(0,e.useEffect)(()=>{c.length>0&&!I.current&&(console.log("\uD83D\uDCE5 Loading initial messages:",c.length),c.forEach(a=>{H({role:a.role,content:a.content,timestamp:new Date(a.timestamp||a.created_at).getTime()})}),I.current=!0)},[c,H]),(0,e.useEffect)(()=>{k.current&&k.current.scrollTo({top:k.current.scrollHeight,behavior:"smooth"})},[v,y]);let J=a=>a?a.includes("business")||a.includes("marketing")?"\uD83D\uDCBC":a.includes("code")||a.includes("programming")?"\uD83D\uDCBB":a.includes("creative")||a.includes("design")?"\uD83C\uDFA8":a.includes("education")||a.includes("learning")?"\uD83D\uDCDA":a.includes("health")||a.includes("medical")?"\uD83C\uDFE5":a.includes("finance")||a.includes("money")?"\uD83D\uDCB0":"\uD83E\uDD16":"\uD83E\uDD16",K=async()=>{if(f.trim()&&!w){if(!a||!a.id){console.warn("⚠️ Cannot send message: expert not loaded"),alert("Please wait for the expert to load before sending messages.");return}g(""),p?(console.log("\uD83D\uDCE4 Sending message with expert:",{expertId:a?.id,message:f.substring(0,50)+"..."}),F(f)):(console.log("\uD83D\uDD04 Using fallback API for message:",f),alert("Real-time chat is not available. Please log in or refresh the page to enable streaming features."))}};return(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-6",children:[(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)(h(),{href:a?`/expert/${a.id}`:"/",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors",children:[(0,d.jsx)(i.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:a?"Back to Profile":"Back to Home"})]}),a&&(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Chatting with"}),(0,d.jsx)("p",{className:"font-semibold text-gray-900",children:a.name})]}),a.imageUrl?(0,d.jsx)(j.default,{src:(0,r.L)(a.imageUrl),alt:a.name,width:40,height:40,className:"w-10 h-10 object-cover rounded-full border-2 border-gray-200"}):(0,d.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center text-white text-sm",style:{backgroundColor:"#1E3A8A"},children:J(a.labels)})]})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"lg:col-span-3",children:(0,d.jsx)(o.Zp,{className:"bg-white/80 backdrop-blur-sm border border-gray-200 shadow-xl",children:(0,d.jsxs)("div",{className:"p-6",children:[a&&(0,d.jsx)("div",{className:"mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[a.imageUrl?(0,d.jsx)(j.default,{src:(0,r.L)(a.imageUrl),alt:a.name,width:48,height:48,className:"w-12 h-12 object-cover rounded-full border-2 border-white shadow-sm"}):(0,d.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center text-white text-lg shadow-sm",style:{backgroundColor:"#1E3A8A"},children:J(a.labels)}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.description}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,d.jsx)("span",{className:"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full",children:a.model}),(0,d.jsx)("span",{className:`text-xs ${p?"text-green-600":"text-red-600"}`,children:p?"● Online":"● Offline"})]})]})]})}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)(A,{isConnected:p,connectionError:q,onReconnect:u}),q&&q.includes("log in")&&(0,d.jsx)("div",{className:"mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,d.jsxs)("p",{className:"text-sm text-blue-700",children:["\uD83D\uDCA1 ",(0,d.jsx)("strong",{children:"Tip:"})," You can still use the regular chat without real-time features.",(0,d.jsx)(h(),{href:"/login",className:"underline ml-1",children:"Log in"})," to enable streaming chat."]})})]}),z&&(0,d.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-red-600",children:z}),(0,d.jsx)("button",{onClick:G,className:"text-red-600 hover:text-red-800 text-sm underline",children:"Dismiss"})]})}),(0,d.jsxs)("div",{ref:k,className:"h-[60vh] overflow-y-auto space-y-4 mb-6 px-2",style:{scrollbarWidth:"thin"},children:[0===v.length&&a&&(0,d.jsxs)("div",{className:"text-center mt-20",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCAC"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-700 mb-2",children:"Start a Conversation"}),(0,d.jsxs)("p",{className:"text-gray-500",children:["Hello! I'm ",a.name,". ",a.description," How can I assist you today?"]})]}),v.filter(a=>a&&a.id).map(b=>(0,d.jsx)(s,{role:b.role,content:b.content||"",isStreaming:b.isStreaming,timestamp:b.timestamp,cost:b.cost,tokens:b.tokens,expertName:a?.name,expertImageUrl:a?.imageUrl,expertIcon:a?J(a.labels):"\uD83E\uDD16"},b.id)),y&&(0,d.jsx)(t,{expertName:a?.name,expertImageUrl:a?.imageUrl,expertIcon:a?J(a.labels):"\uD83E\uDD16"})]}),(0,d.jsxs)("form",{className:"flex gap-3 items-end",onSubmit:a=>{a.preventDefault(),K()},children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsx)(m.p,{value:f,onChange:a=>g(a.target.value),placeholder:a?`Ask ${a.name} anything...`:"Type your message...",disabled:w||!p,className:"w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200",autoFocus:!0,onKeyDown:a=>{"Enter"!==a.key||a.shiftKey||(a.preventDefault(),K())}})}),(0,d.jsx)(n.$,{type:"submit",disabled:w||!f.trim()||!p,className:"px-6 py-3 rounded-xl text-white font-medium transition-all duration-200 hover:shadow-lg",style:{backgroundColor:"#1E3A8A"},children:(0,d.jsx)(l,{className:"w-5 h-5"})})]})]})})}),(0,d.jsxs)("div",{className:"lg:col-span-1 space-y-4",children:[(0,d.jsx)(x,{currentCost:C,totalTokens:D,balanceUpdate:E,isStreaming:w}),a&&(0,d.jsxs)(o.Zp,{className:"p-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Session Info"}),(0,d.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Expert:"}),(0,d.jsx)("span",{className:"font-medium",children:a.name})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Model:"}),(0,d.jsx)("span",{className:"font-medium",children:a.model})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Messages:"}),(0,d.jsx)("span",{className:"font-medium",children:v.length})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Status:"}),(0,d.jsx)("span",{className:`font-medium ${p?"text-green-600":"text-red-600"}`,children:p?"Connected":"Disconnected"})]})]})]})]})]})]})})};function D(){let a=(0,f.useSearchParams)(),b=a.get("expertId"),c=a.get("threadId"),[g,j]=(0,e.useState)(null),[k,l]=(0,e.useState)([]),[m,n]=(0,e.useState)(null),[o,p]=(0,e.useState)(!!b),[q,r]=(0,e.useState)(!1),[s,t]=(0,e.useState)(null);return s?(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center max-w-md mx-auto p-6",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"⚠️"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-700 mb-2",children:"Error Loading Chat"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:s}),(0,d.jsxs)(h(),{href:"/",className:"inline-flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors",children:[(0,d.jsx)(i.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Back to Home"})]})]})}):o||q?(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4",style:{borderColor:"#1E3A8A"}}),(0,d.jsx)("p",{className:"text-gray-600",children:o?`Loading expert (ID: ${b})...`:"Loading chat history..."}),(0,d.jsxs)("p",{className:"text-sm text-gray-400 mt-2",children:["Debug: expertId=",b,", threadId=",c]})]})}):(console.log("\uD83D\uDD0D ChatComponent render:",{expert:g?{id:g.id,name:g.name}:null,expertId:b,isLoadingExpert:o,error:s}),(0,d.jsx)(C,{expert:g,sessionId:m||void 0,initialMessages:k}))}function E(){return(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)("div",{children:"Loading..."}),children:(0,d.jsx)(D,{})})}},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28559:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},31221:(a,b,c)=>{Promise.resolve().then(c.bind(c,22826))},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41862:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},53989:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["chat",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,8973)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\chat\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\chat\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/chat/page",pathname:"/chat",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/chat/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89365:(a,b,c)=>{Promise.resolve().then(c.bind(c,8973))},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>f});var d=c(60687);c(43210);var e=c(4780);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}},91645:a=>{"use strict";a.exports=require("net")},93613:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,842,928,474,263],()=>b(b.s=53989));module.exports=c})();