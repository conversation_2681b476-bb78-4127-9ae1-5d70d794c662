"use client";

import React, { createContext, useReducer, useEffect, ReactNode } from "react";
import { FilterState, FilterContextType, FilterType, TimelineType } from "@/types/filters";

// Initial state
const initialState: FilterState = {
  selectedFilter: 'recommended',
  selectedTimeline: 'last-30-days',
  searchQuery: '',
  isLoading: false
};

// Action types
type FilterAction =
  | { type: 'SET_FILTER'; payload: FilterType }
  | { type: 'SET_TIMELINE'; payload: TimelineType }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'RESET_FILTERS' }
  | { type: 'LOAD_FROM_STORAGE'; payload: Partial<FilterState> };

// Reducer
const filterReducer = (state: FilterState, action: FilterAction): FilterState => {
  switch (action.type) {
    case 'SET_FILTER':
      return { ...state, selectedFilter: action.payload };
    case 'SET_TIMELINE':
      return { ...state, selectedTimeline: action.payload };
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'RESET_FILTERS':
      return { ...initialState };
    case 'LOAD_FROM_STORAGE':
      return { ...state, ...action.payload };
    default:
      return state;
  }
};

// Context
export const FilterContext = createContext<FilterContextType | undefined>(undefined);

// Storage keys
const STORAGE_KEYS = {
  FILTER: 'marketplace_filter',
  TIMELINE: 'marketplace_timeline',
  SEARCH: 'marketplace_search'
};

// Provider component
interface FilterProviderProps {
  children: ReactNode;
}

export const FilterProvider: React.FC<FilterProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(filterReducer, initialState);

  // Load from session storage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedFilter = sessionStorage.getItem(STORAGE_KEYS.FILTER) as FilterType;
        const savedTimeline = sessionStorage.getItem(STORAGE_KEYS.TIMELINE) as TimelineType;
        const savedSearch = sessionStorage.getItem(STORAGE_KEYS.SEARCH);

        const savedState: Partial<FilterState> = {};

        if (savedFilter && ['trending', 'most-popular', 'top-rated', 'recommended', 'newest'].includes(savedFilter)) {
          savedState.selectedFilter = savedFilter;
        }

        if (savedTimeline && ['last-7-days', 'last-30-days', 'all-time'].includes(savedTimeline)) {
          savedState.selectedTimeline = savedTimeline;
        }

        if (savedSearch) {
          savedState.searchQuery = savedSearch;
        }

        if (Object.keys(savedState).length > 0) {
          dispatch({ type: 'LOAD_FROM_STORAGE', payload: savedState });
        }
      } catch (error) {
        console.warn('Failed to load filter state from session storage:', error);
      }
    }
  }, []);

  // Save to session storage when state changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        sessionStorage.setItem(STORAGE_KEYS.FILTER, state.selectedFilter);
        sessionStorage.setItem(STORAGE_KEYS.TIMELINE, state.selectedTimeline);
        sessionStorage.setItem(STORAGE_KEYS.SEARCH, state.searchQuery);
      } catch (error) {
        console.warn('Failed to save filter state to session storage:', error);
      }
    }
  }, [state.selectedFilter, state.selectedTimeline, state.searchQuery]);

  // Actions
  const setFilter = (filter: FilterType) => {
    dispatch({ type: 'SET_FILTER', payload: filter });
  };

  const setTimeline = (timeline: TimelineType) => {
    dispatch({ type: 'SET_TIMELINE', payload: timeline });
  };

  const setSearchQuery = (query: string) => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query });
  };

  const resetFilters = () => {
    dispatch({ type: 'RESET_FILTERS' });
    // Clear session storage
    if (typeof window !== 'undefined') {
      try {
        sessionStorage.removeItem(STORAGE_KEYS.FILTER);
        sessionStorage.removeItem(STORAGE_KEYS.TIMELINE);
        sessionStorage.removeItem(STORAGE_KEYS.SEARCH);
      } catch (error) {
        console.warn('Failed to clear filter state from session storage:', error);
      }
    }
  };

  const contextValue: FilterContextType = {
    state,
    actions: {
      setFilter,
      setTimeline,
      setSearchQuery,
      resetFilters
    }
  };

  return (
    <FilterContext.Provider value={contextValue}>
      {children}
    </FilterContext.Provider>
  );
};