"use strict";exports.id=304,exports.ids=[304],exports.modules={54304:(a,b,c)=>{c.d(b,{UC:()=>_,B8:()=>Z,bL:()=>Y,l9:()=>$});var d=c(43210),e=c(70569),f=c(11273),g=c(98599),h=c(8730),i=c(60687),j=new WeakMap;function k(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=l(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function l(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],j.set(this,!0)}set(a,b){return j.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=l(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],m=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{m||k[a-1]!==b||(m=!0);let c=k[m?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=k(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=k(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return k(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}});var m=c(96963),n=c(14163),o=c(13495),p=c(65551),q=d.createContext(void 0);function r(a){let b=d.useContext(q);return a||b||"ltr"}var s="rovingFocusGroup.onEntryFocus",t={bubbles:!1,cancelable:!0},u="RovingFocusGroup",[v,w,x]=function(a){let b=a+"CollectionProvider",[c,e]=(0,f.A)(b),[j,k]=c(b,{collectionRef:{current:null},itemMap:new Map}),l=a=>{let{scope:b,children:c}=a,e=d.useRef(null),f=d.useRef(new Map).current;return(0,i.jsx)(j,{scope:b,itemMap:f,collectionRef:e,children:c})};l.displayName=b;let m=a+"CollectionSlot",n=(0,h.TL)(m),o=d.forwardRef((a,b)=>{let{scope:c,children:d}=a,e=k(m,c),f=(0,g.s)(b,e.collectionRef);return(0,i.jsx)(n,{ref:f,children:d})});o.displayName=m;let p=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,h.TL)(p),s=d.forwardRef((a,b)=>{let{scope:c,children:e,...f}=a,h=d.useRef(null),j=(0,g.s)(b,h),l=k(p,c);return d.useEffect(()=>(l.itemMap.set(h,{ref:h,...f}),()=>void l.itemMap.delete(h))),(0,i.jsx)(r,{...{[q]:""},ref:j,children:e})});return s.displayName=p,[{Provider:l,Slot:o,ItemSlot:s},function(b){let c=k(a+"CollectionConsumer",b);return d.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},e]}(u),[y,z]=(0,f.A)(u,[x]),[A,B]=y(u),C=d.forwardRef((a,b)=>(0,i.jsx)(v.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,i.jsx)(v.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,i.jsx)(D,{...a,ref:b})})}));C.displayName=u;var D=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:f,loop:h=!1,dir:j,currentTabStopId:k,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:m,onEntryFocus:q,preventScrollOnEntryFocus:v=!1,...x}=a,y=d.useRef(null),z=(0,g.s)(b,y),B=r(j),[C,D]=(0,p.i)({prop:k,defaultProp:l??null,onChange:m,caller:u}),[E,F]=d.useState(!1),G=(0,o.c)(q),I=w(c),J=d.useRef(!1),[K,L]=d.useState(0);return d.useEffect(()=>{let a=y.current;if(a)return a.addEventListener(s,G),()=>a.removeEventListener(s,G)},[G]),(0,i.jsx)(A,{scope:c,orientation:f,dir:B,loop:h,currentTabStopId:C,onItemFocus:d.useCallback(a=>D(a),[D]),onItemShiftTab:d.useCallback(()=>F(!0),[]),onFocusableItemAdd:d.useCallback(()=>L(a=>a+1),[]),onFocusableItemRemove:d.useCallback(()=>L(a=>a-1),[]),children:(0,i.jsx)(n.sG.div,{tabIndex:E||0===K?-1:0,"data-orientation":f,...x,ref:z,style:{outline:"none",...a.style},onMouseDown:(0,e.m)(a.onMouseDown,()=>{J.current=!0}),onFocus:(0,e.m)(a.onFocus,a=>{let b=!J.current;if(a.target===a.currentTarget&&b&&!E){let b=new CustomEvent(s,t);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=I().filter(a=>a.focusable);H([a.find(a=>a.active),a.find(a=>a.id===C),...a].filter(Boolean).map(a=>a.ref.current),v)}}J.current=!1}),onBlur:(0,e.m)(a.onBlur,()=>F(!1))})})}),E="RovingFocusGroupItem",F=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:f=!0,active:g=!1,tabStopId:h,children:j,...k}=a,l=(0,m.B)(),o=h||l,p=B(E,c),q=p.currentTabStopId===o,r=w(c),{onFocusableItemAdd:s,onFocusableItemRemove:t,currentTabStopId:u}=p;return d.useEffect(()=>{if(f)return s(),()=>t()},[f,s,t]),(0,i.jsx)(v.ItemSlot,{scope:c,id:o,focusable:f,active:g,children:(0,i.jsx)(n.sG.span,{tabIndex:q?0:-1,"data-orientation":p.orientation,...k,ref:b,onMouseDown:(0,e.m)(a.onMouseDown,a=>{f?p.onItemFocus(o):a.preventDefault()}),onFocus:(0,e.m)(a.onFocus,()=>p.onItemFocus(o)),onKeyDown:(0,e.m)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void p.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return G[e]}(a,p.orientation,p.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=r().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=p.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>H(c))}}),children:"function"==typeof j?j({isCurrentTabStop:q,hasTabStop:null!=u}):j})})});F.displayName=E;var G={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function H(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var I=c(46059),J="Tabs",[K,L]=(0,f.A)(J,[z]),M=z(),[N,O]=K(J),P=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,onValueChange:e,defaultValue:f,orientation:g="horizontal",dir:h,activationMode:j="automatic",...k}=a,l=r(h),[o,q]=(0,p.i)({prop:d,onChange:e,defaultProp:f??"",caller:J});return(0,i.jsx)(N,{scope:c,baseId:(0,m.B)(),value:o,onValueChange:q,orientation:g,dir:l,activationMode:j,children:(0,i.jsx)(n.sG.div,{dir:l,"data-orientation":g,...k,ref:b})})});P.displayName=J;var Q="TabsList",R=d.forwardRef((a,b)=>{let{__scopeTabs:c,loop:d=!0,...e}=a,f=O(Q,c),g=M(c);return(0,i.jsx)(C,{asChild:!0,...g,orientation:f.orientation,dir:f.dir,loop:d,children:(0,i.jsx)(n.sG.div,{role:"tablist","aria-orientation":f.orientation,...e,ref:b})})});R.displayName=Q;var S="TabsTrigger",T=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,disabled:f=!1,...g}=a,h=O(S,c),j=M(c),k=W(h.baseId,d),l=X(h.baseId,d),m=d===h.value;return(0,i.jsx)(F,{asChild:!0,...j,focusable:!f,active:m,children:(0,i.jsx)(n.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":l,"data-state":m?"active":"inactive","data-disabled":f?"":void 0,disabled:f,id:k,...g,ref:b,onMouseDown:(0,e.m)(a.onMouseDown,a=>{f||0!==a.button||!1!==a.ctrlKey?a.preventDefault():h.onValueChange(d)}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&h.onValueChange(d)}),onFocus:(0,e.m)(a.onFocus,()=>{let a="manual"!==h.activationMode;m||f||!a||h.onValueChange(d)})})})});T.displayName=S;var U="TabsContent",V=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,forceMount:f,children:g,...h}=a,j=O(U,c),k=W(j.baseId,e),l=X(j.baseId,e),m=e===j.value,o=d.useRef(m);return d.useEffect(()=>{let a=requestAnimationFrame(()=>o.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,i.jsx)(I.C,{present:f||m,children:({present:c})=>(0,i.jsx)(n.sG.div,{"data-state":m?"active":"inactive","data-orientation":j.orientation,role:"tabpanel","aria-labelledby":k,hidden:!c,id:l,tabIndex:0,...h,ref:b,style:{...a.style,animationDuration:o.current?"0s":void 0},children:c&&g})})});function W(a,b){return`${a}-trigger-${b}`}function X(a,b){return`${a}-content-${b}`}V.displayName=U;var Y=P,Z=R,$=T,_=V}};