(()=>{var a={};a.id=916,a.ids=[916],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(a,b,c)=>{"use strict";c.d(b,{L:()=>h,cn:()=>g});var d=c(49384),e=c(82348);let f=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001";function g(...a){return(0,e.QP)((0,d.$)(a))}function h(a){return`${f}/${a.startsWith("/")?a.replace("/",""):a}`}},9718:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>S});var d=c(60687),e=c(16189),f=c(43210),g=c.n(f),h=c(30474),i=c(62185),j=c(41862),k=c(62688);let l=(0,k.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var m=c(56085);let n=({onExpertCreated:a,onCancel:b})=>{let[c,e]=(0,f.useState)({name:"",description:"",systemPrompt:"",model:"gpt-4o-mini",pricingPercentage:"0.00",isPublic:!1}),[k,n]=(0,f.useState)([]),[o,p]=(0,f.useState)(""),[q,r]=(0,f.useState)(null),[s,t]=(0,f.useState)(null),[u,v]=(0,f.useState)(!1),[w,x]=(0,f.useState)(null),[y,z]=(0,f.useState)(!1),[A,B]=(0,f.useState)(!1),[C,D]=(0,f.useState)(null),[E,F]=(0,f.useState)(null),[G,H]=(0,f.useState)({quality:"standard",size:"1024x1024",style:"natural"}),I=()=>{if(!E?.imageGeneration)return null;let{quality:a,size:b}=G;return"low"===a?E.imageGeneration.low?.estimated||null:"standard"===a?"1024x1024"===b?E.imageGeneration.standard?.estimated||null:E.imageGeneration.standardWide?.estimated||null:"high"===a?"1024x1024"===b?E.imageGeneration.high?.estimated||null:E.imageGeneration.highWide?.estimated||null:null};g().useEffect(()=>{(async()=>{try{let a=await i.FH.get("/api/ai-generation/costs");a.success&&F(a.costs)}catch(a){console.error("Failed to load AI costs:",a)}})()},[]);let J=a=>{let{name:b,value:c,type:d,checked:f}=a.target;e(a=>({...a,[b]:"checkbox"===d?f:c}))},K=()=>{o.trim()&&k.length<5&&!k.includes(o.trim())&&(n(a=>[...a,o.trim()]),p(""))},L=async()=>{if(!c.name||!c.systemPrompt)return void x("Please fill in the name and system prompt before generating labels.");z(!0),x(null);try{let a=await i.FH.post("/api/ai-generation/labels",{body:{name:c.name,description:c.description,systemPrompt:c.systemPrompt,model:c.model}});a.success&&(n(a.labels),console.log(`Labels generated successfully! Cost: ${a.cost} IDR`))}catch(a){console.error("Label generation error:",a),a.response?.status===402?x("Insufficient balance for AI label generation. Please top up your account."):x(a.response?.data?.message||"Failed to generate labels. Please try again.")}finally{z(!1)}},M=async()=>{if(!c.name)return void x("Please fill in the expert name before generating an image.");B(!0),x(null);try{let a=await i.FH.post("/api/ai-generation/image",{body:{name:c.name,description:c.description,labels:k,quality:G.quality,size:G.size,style:G.style}});a.success&&(D(a.imageUrl),F(b=>({...b,imageGeneration:{...b?.imageGeneration,actual:a.cost}})),console.log(`Image generated successfully with ${a.model}! Cost: ${a.cost} IDR`),a.fallback&&x("Content policy violation detected. Using default image instead."))}catch(a){console.error("Image generation error:",a),a.response?.status===402?x("Insufficient balance for AI image generation. Please top up your account."):x(a.response?.data?.message||"Failed to generate image. Please try again.")}finally{B(!1)}},N=async b=>{b.preventDefault(),v(!0),x(null);try{let b=new FormData;b.append("name",c.name),b.append("description",c.description),b.append("systemPrompt",c.systemPrompt),b.append("model",c.model),b.append("pricingPercentage",c.pricingPercentage),b.append("isPublic",c.isPublic.toString()),b.append("labels",JSON.stringify(k)),q&&b.append("file",q),s&&b.append("image",s);let d=await i.FH.createExpert(b);d.success?(a?.(d.expert),e({name:"",description:"",systemPrompt:"",model:"gpt-4o-mini",pricingPercentage:"0.00",isPublic:!1}),n([]),p(""),r(null),t(null)):x(d.error||"Failed to create expert")}catch(a){x(a.message||"Failed to create expert")}finally{v(!1)}};return(0,d.jsxs)("div",{className:"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-center mb-6",children:"Create AI Expert"}),(0,d.jsx)("p",{className:"text-gray-600 text-center mb-8",children:"Fill in the details to create a new AI expert profile."}),w&&(0,d.jsx)("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:w}),(0,d.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),(0,d.jsx)("input",{type:"text",id:"name",name:"name",value:c.name,onChange:J,placeholder:"e.g., Creative Writer",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,d.jsx)("textarea",{id:"description",name:"description",value:c.description,onChange:J,placeholder:"Describe the expert's capabilities and purpose.",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("label",{htmlFor:"imageFile",className:"block text-sm font-medium text-gray-700",children:"Upload Image (Optional)"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)("select",{value:G.quality,onChange:a=>H(b=>({...b,quality:a.target.value})),className:"text-xs px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500",disabled:A,children:[(0,d.jsx)("option",{value:"low",children:"Low Quality (272 tokens)"}),(0,d.jsx)("option",{value:"standard",children:"Standard (1360 tokens)"}),(0,d.jsx)("option",{value:"high",children:"High Quality (2720 tokens)"})]}),(0,d.jsxs)("select",{value:G.size,onChange:a=>H(b=>({...b,size:a.target.value})),className:"text-xs px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500",disabled:A,children:[(0,d.jsx)("option",{value:"1024x1024",children:"1024\xd71024"}),(0,d.jsx)("option",{value:"1024x1536",children:"1024\xd71536"}),(0,d.jsx)("option",{value:"1536x1024",children:"1536\xd71024"})]}),(0,d.jsx)("button",{type:"button",onClick:M,disabled:A||!c.name,className:"inline-flex items-center px-3 py-1 text-xs font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-md hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed",children:A?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(j.A,{className:"w-3 h-3 mr-1 animate-spin"}),"Generating..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(l,{className:"w-3 h-3 mr-1"}),"GPT-Image-1",I()&&(0,d.jsxs)("span",{className:"ml-1 text-gray-500",children:["(~",I()," IDR)"]})]})})]})]}),(0,d.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[(0,d.jsx)("input",{type:"file",id:"imageFile",onChange:a=>{let b=a.target.files?.[0];if(b){let a=b.name.toLowerCase().substring(b.name.lastIndexOf("."));if(b.size>0xa00000){x("Image file size must be less than 10MB"),t(null);return}["image/png","image/jpeg","image/jpg","image/gif","image/webp"].includes(b.type)||[".png",".jpg",".jpeg",".gif",".webp"].includes(a)?(t(b),x(null)):(x("Invalid image file type. Supported formats: PNG, JPG, JPEG, GIF, WEBP"),t(null))}},accept:".png,.jpg,.jpeg,.gif,.webp",className:"hidden"}),(0,d.jsxs)("label",{htmlFor:"imageFile",className:"cursor-pointer",children:[(0,d.jsx)("div",{className:"text-gray-400 mb-2",children:(0,d.jsx)("svg",{className:"mx-auto h-12 w-12",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,d.jsx)("path",{d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20v-2a2 2 0 012-2h8a2 2 0 012 2v2m0 0v8a2 2 0 01-2 2H8a2 2 0 01-2-2v-8m8-2V8a2 2 0 00-2-2H8a2 2 0 00-2 2v2m0 0h12m0 0v8a2 2 0 01-2 2h-8a2 2 0 01-2-2v-8z",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,d.jsx)("p",{className:"text-blue-600 hover:text-blue-500",children:"Upload an image or drag and drop"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"PNG, JPG, GIF, WEBP up to 10MB"})]}),s&&(0,d.jsxs)("p",{className:"mt-2 text-sm text-green-600",children:["Selected: ",s.name]})]}),C&&(0,d.jsxs)("div",{className:"mt-4 p-4 bg-purple-50 border border-purple-200 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-purple-900",children:"AI Generated Image"}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("button",{type:"button",onClick:()=>{C&&fetch(C).then(a=>a.blob()).then(a=>{t(new File([a],"ai-generated-image.png",{type:"image/png"})),D(null)}).catch(a=>{console.error("Failed to use generated image:",a),x("Failed to use generated image. Please try again.")})},className:"px-3 py-1 text-xs font-medium text-white bg-purple-600 rounded hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500",children:"Use This Image"}),(0,d.jsx)("button",{type:"button",onClick:()=>D(null),className:"px-3 py-1 text-xs font-medium text-purple-600 bg-white border border-purple-300 rounded hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-purple-500",children:"Discard"})]})]}),(0,d.jsx)("div",{className:"flex justify-center",children:(0,d.jsx)(h.default,{src:C,alt:"AI Generated Expert Image",width:300,height:192,className:"max-w-xs max-h-48 rounded-lg shadow-md object-cover"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"knowledgeBaseFile",className:"block text-sm font-medium text-gray-700 mb-2",children:"Upload Knowledge Base (Optional)"}),(0,d.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[(0,d.jsx)("input",{type:"file",id:"knowledgeBaseFile",onChange:a=>{let b=a.target.files?.[0];if(b){let a=b.name.toLowerCase().substring(b.name.lastIndexOf("."));["application/pdf","text/plain","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/markdown","application/json"].includes(b.type)||[".pdf",".txt",".docx",".doc",".md",".json"].includes(a)?(r(b),x(null)):(x("Invalid knowledge base file type. Supported formats: PDF, TXT, DOCX, DOC, MD, JSON"),r(null))}},accept:".pdf,.txt,.docx,.doc,.md,.json",className:"hidden"}),(0,d.jsxs)("label",{htmlFor:"knowledgeBaseFile",className:"cursor-pointer",children:[(0,d.jsx)("div",{className:"text-gray-400 mb-2",children:(0,d.jsx)("svg",{className:"mx-auto h-12 w-12",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,d.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,d.jsx)("p",{className:"text-blue-600 hover:text-blue-500",children:"Upload a knowledge base file or drag and drop"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"PDF, TXT, DOCX, DOC, MD, JSON"})]}),q&&(0,d.jsxs)("p",{className:"mt-2 text-sm text-green-600",children:["Selected: ",q.name]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"systemPrompt",className:"block text-sm font-medium text-gray-700 mb-2",children:"System Prompt"}),(0,d.jsx)("textarea",{id:"systemPrompt",name:"systemPrompt",value:c.systemPrompt,onChange:J,placeholder:"Enter the system prompt that defines the expert's behavior.",rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"model",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Selection"}),(0,d.jsx)("select",{id:"model",name:"model",value:c.model,onChange:J,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"gpt-3.5-turbo",label:"GPT-3.5 Turbo"},{value:"gpt-4",label:"GPT-4"},{value:"gpt-4-turbo",label:"GPT-4 Turbo"},{value:"gpt-4o",label:"GPT-4o"},{value:"gpt-4o-mini",label:"GPT-4o Mini"}].map(a=>(0,d.jsx)("option",{value:a.value,children:a.label},a.value))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"pricingPercentage",className:"block text-sm font-medium text-gray-700 mb-2",children:"Pricing (% of token usage)"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{type:"number",id:"pricingPercentage",name:"pricingPercentage",value:c.pricingPercentage,onChange:J,min:"0",max:"100",step:"0.01",className:"w-full px-3 py-2 pr-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"absolute right-3 top-2 text-gray-500",children:"%"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,d.jsx)("input",{type:"checkbox",name:"isPublic",checked:c.isPublic,onChange:J,className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Make this expert public (others can discover and use it)"})]}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1 ml-7",children:c.isPublic?"This expert will be visible to all users":"This expert will be private (unlisted)"})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Labels (max 5)"}),(0,d.jsx)("button",{type:"button",onClick:L,disabled:y||!c.name||!c.systemPrompt,className:"inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:y?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(j.A,{className:"w-3 h-3 mr-1 animate-spin"}),"Generating..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"AI Generate",E?.labelGeneration&&(0,d.jsxs)("span",{className:"ml-1 text-gray-500",children:["(~",E.labelGeneration.estimated," IDR)"]})]})})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("input",{type:"text",value:o,onChange:a=>p(a.target.value),onKeyPress:a=>{"Enter"===a.key&&(a.preventDefault(),K())},placeholder:"Add a label...",maxLength:50,className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:k.length>=5}),(0,d.jsx)("button",{type:"button",onClick:K,disabled:!o.trim()||k.length>=5||k.includes(o.trim()),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"Add"})]}),k.length>0&&(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:k.map((a,b)=>(0,d.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800",children:[a,(0,d.jsx)("button",{type:"button",onClick:()=>{n(k.filter((a,c)=>c!==b))},className:"ml-2 text-blue-600 hover:text-blue-800 focus:outline-none",children:"\xd7"})]},b))}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:[k.length,"/5 labels used. Labels help users discover your expert."]})]})]}),(E?.labelGeneration?.actual||E?.imageGeneration?.actual)&&(0,d.jsxs)("div",{className:"p-4 bg-gray-50 border border-gray-200 rounded-lg",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"AI Generation Costs Used"}),(0,d.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[E.labelGeneration?.actual&&(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Label Generation:"}),(0,d.jsxs)("span",{className:"font-medium",children:[E.labelGeneration.actual," IDR"]})]}),E.imageGeneration?.actual&&(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Image Generation:"}),(0,d.jsxs)("span",{className:"font-medium",children:[E.imageGeneration.actual," IDR"]})]}),(0,d.jsx)("div",{className:"border-t border-gray-300 pt-1 mt-2",children:(0,d.jsxs)("div",{className:"flex justify-between font-medium text-gray-900",children:[(0,d.jsx)("span",{children:"Total:"}),(0,d.jsxs)("span",{children:[(E.labelGeneration?.actual||0)+(E.imageGeneration?.actual||0)," IDR"]})]})})]})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-4",children:[b&&(0,d.jsx)("button",{type:"button",onClick:b,className:"px-6 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500",children:"Cancel"}),(0,d.jsx)("button",{type:"submit",disabled:u,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:u?"Creating...":"Create Expert"})]})]})]})};var o=c(4780);let p=({expert:a,onExpertUpdated:b,onCancel:c})=>{let[e,g]=(0,f.useState)({name:a.name,description:a.description||"",systemPrompt:a.systemPrompt,model:a.model,pricingPercentage:a.pricingPercentage.toString(),isPublic:a.isPublic||!1}),[j,k]=(0,f.useState)(a.labels||[]),[l,m]=(0,f.useState)(""),[n,p]=(0,f.useState)(null),[q,r]=(0,f.useState)(null),[s,t]=(0,f.useState)(!1),[u,v]=(0,f.useState)(null),w=a=>{let{name:b,value:c,type:d,checked:e}=a.target;g(a=>({...a,[b]:"checkbox"===d?e:c}))},x=()=>{l.trim()&&j.length<5&&!j.includes(l.trim())&&(k(a=>[...a,l.trim()]),m(""))},y=async c=>{if(c.preventDefault(),!e.name.trim()||!e.systemPrompt.trim())return void v("Name and system prompt are required");let d=parseFloat(e.pricingPercentage);if(isNaN(d)||d<0||d>100)return void v("Pricing percentage must be between 0 and 100");t(!0),v(null);try{let c=await i.FH.updateExpert(a.id,{name:e.name.trim(),description:e.description.trim(),systemPrompt:e.systemPrompt.trim(),model:e.model,pricingPercentage:d,isPublic:e.isPublic,labels:j},n,q);c.success?b?.(c.expert):v(c.error||"Failed to update expert")}catch(a){v(a.message||"Failed to update expert")}finally{t(!1)}};return(0,d.jsxs)("div",{className:"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Edit AI Expert"}),u&&(0,d.jsx)("div",{className:"mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded",children:u}),(0,d.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Expert Name *"}),(0,d.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:w,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter expert name"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,d.jsx)("textarea",{id:"description",name:"description",value:e.description,onChange:w,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Describe what this expert does"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"systemPrompt",className:"block text-sm font-medium text-gray-700 mb-2",children:"System Prompt *"}),(0,d.jsx)("textarea",{id:"systemPrompt",name:"systemPrompt",value:e.systemPrompt,onChange:w,required:!0,rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Define the expert's behavior and knowledge"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"model",className:"block text-sm font-medium text-gray-700 mb-2",children:"AI Model"}),(0,d.jsx)("select",{id:"model",name:"model",value:e.model,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"gpt-3.5-turbo",label:"GPT-3.5 Turbo"},{value:"gpt-4",label:"GPT-4"},{value:"gpt-4-turbo",label:"GPT-4 Turbo"},{value:"gpt-4o",label:"GPT-4o"},{value:"gpt-4o-mini",label:"GPT-4o Mini"}].map(a=>(0,d.jsx)("option",{value:a.value,children:a.label},a.value))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"pricingPercentage",className:"block text-sm font-medium text-gray-700 mb-2",children:"Pricing Percentage (%)"}),(0,d.jsx)("input",{type:"number",id:"pricingPercentage",name:"pricingPercentage",value:e.pricingPercentage,onChange:w,min:"0",max:"100",step:"0.01",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0.00"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"checkbox",name:"isPublic",checked:e.isPublic,onChange:w,className:"rounded border-gray-300 focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Make this expert public"})]}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Public experts can be discovered and used by other users"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"knowledgeBase",className:"block text-sm font-medium text-gray-700 mb-2",children:"Knowledge Base File (Optional)"}),(0,d.jsx)("input",{type:"file",id:"knowledgeBase",onChange:a=>{let b=a.target.files?.[0];if(b){let a=b.name.toLowerCase().substring(b.name.lastIndexOf("."));["application/pdf","text/plain","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/markdown","application/json"].includes(b.type)||[".pdf",".txt",".docx",".doc",".md",".json"].includes(a)?(p(b),v(null)):(v("Invalid knowledge base file type. Supported formats: PDF, TXT, DOCX, DOC, MD, JSON"),p(null))}},accept:".pdf,.txt,.docx,.doc,.md,.json",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Upload a new file to update the knowledge base. Supported: PDF, TXT, DOCX, DOC, MD, JSON"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"image",className:"block text-sm font-medium text-gray-700 mb-2",children:"Expert Image (Optional)"}),(0,d.jsx)("input",{type:"file",id:"image",onChange:a=>{let b=a.target.files?.[0];if(b){let a=b.name.toLowerCase().substring(b.name.lastIndexOf("."));if(b.size>0xa00000){v("Image file size must be less than 10MB"),r(null);return}["image/png","image/jpeg","image/jpg","image/gif","image/webp"].includes(b.type)||[".png",".jpg",".jpeg",".gif",".webp"].includes(a)?(r(b),v(null)):(v("Invalid image file type. Supported formats: PNG, JPG, JPEG, GIF, WEBP"),r(null))}},accept:"image/*",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Upload a new image to replace the current one. Max 10MB. Supported: PNG, JPG, JPEG, GIF, WEBP"}),a.imageUrl&&(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"Current image:"}),(0,d.jsx)(h.default,{src:(0,o.L)(a.imageUrl),alt:"Current expert image",width:64,height:64,className:"w-16 h-16 object-cover rounded border mt-1"})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Labels (Optional)"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("input",{type:"text",value:l,onChange:a=>m(a.target.value),onKeyPress:a=>{"Enter"===a.key&&(a.preventDefault(),x())},maxLength:50,className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Add a label (max 50 characters)"}),(0,d.jsx)("button",{type:"button",onClick:x,disabled:!l.trim()||j.length>=5||j.includes(l.trim()),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"Add"})]}),j.length>0&&(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:j.map((a,b)=>(0,d.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800",children:[a,(0,d.jsx)("button",{type:"button",onClick:()=>{k(a=>a.filter((a,c)=>c!==b))},className:"ml-2 text-blue-600 hover:text-blue-800 focus:outline-none",children:"\xd7"})]},b))}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:[j.length,"/5 labels used. Labels help users discover your expert."]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-4",children:[c&&(0,d.jsx)("button",{type:"button",onClick:c,className:"px-6 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500",children:"Cancel"}),(0,d.jsx)("button",{type:"submit",disabled:s,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Updating...":"Update Expert"})]})]})]})};var q=c(29523),r=c(44493),s=c(63503),t=c(96474),u=c(63143),v=c(13861),w=c(25541),x=c(41312),y=c(23928),z=c(40228);let A=(0,k.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var B=c(53411);let C=(0,k.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var D=c(48730),E=c(85814),F=c.n(E);let G=({onExpertEdit:a,refreshTrigger:b,onCreateExpert:c})=>{let[e,g]=(0,f.useState)([]),[k,l]=(0,f.useState)(!0),[m,n]=(0,f.useState)(null),[p,E]=(0,f.useState)({}),[G,H]=(0,f.useState)(!1),[I,J]=(0,f.useState)(null),K=async()=>{try{l(!0),n(null);let a=await i.FH.listExperts();a.success?(g(a.experts),await L(a.experts)):n(a.error||"Failed to load experts")}catch(a){n(a.message||"Failed to load experts")}finally{l(!1)}},L=async a=>{let b={};await Promise.all(a.map(async a=>{try{let c=await i.FH.getExpertStats(a.id.toString());c.success?b[a.id]={totalUsers:c.stats.totalUsers,totalCommission:c.stats.totalCommission,last30DaysCommission:c.stats.last30DaysCommission,totalSessions:c.stats.totalSessions,totalMessages:c.stats.totalMessages}:b[a.id]={totalUsers:0,totalCommission:0,last30DaysCommission:0,totalSessions:0,totalMessages:0}}catch(c){console.error(`Failed to load stats for expert ${a.id}:`,c),b[a.id]={totalUsers:0,totalCommission:0,last30DaysCommission:0,totalSessions:0,totalMessages:0}}})),E(b)};(0,f.useEffect)(()=>{K()},[b]);let M=a=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0,maximumFractionDigits:0}).format(a),N=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),O=a=>a?a.includes("business")||a.includes("marketing")?"\uD83D\uDCBC":a.includes("code")||a.includes("programming")?"\uD83D\uDCBB":a.includes("creative")||a.includes("design")?"\uD83C\uDFA8":a.includes("education")||a.includes("learning")?"\uD83D\uDCDA":a.includes("health")||a.includes("medical")?"\uD83C\uDFE5":a.includes("finance")||a.includes("money")?"\uD83D\uDCB0":"\uD83E\uDD16":"\uD83E\uDD16";return k?(0,d.jsxs)("div",{className:"flex justify-center items-center p-12",children:[(0,d.jsx)(j.A,{className:"w-8 h-8 animate-spin text-blue-600"}),(0,d.jsx)("span",{className:"ml-3 text-gray-600",children:"Loading experts..."})]}):m?(0,d.jsx)(r.Zp,{className:"p-6 border-red-200 bg-red-50",children:(0,d.jsxs)("div",{className:"text-red-700",children:[(0,d.jsx)("p",{className:"font-semibold mb-2",children:"Error"}),(0,d.jsx)("p",{className:"text-sm mb-4",children:m}),(0,d.jsx)(q.$,{onClick:K,variant:"outline",className:"border-red-300 text-red-700 hover:bg-red-100",children:"Retry"})]})}):0===e.length?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:"Your AI Experts (0)"}),c&&(0,d.jsxs)(q.$,{onClick:c,className:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl transition-all duration-200 hover:shadow-lg flex items-center gap-2",children:[(0,d.jsx)(t.A,{className:"w-5 h-5"}),"Create Expert"]})]}),(0,d.jsx)(r.Zp,{className:"p-12 text-center border-dashed border-2 border-gray-300",children:(0,d.jsxs)("div",{className:"text-gray-500",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"\uD83E\uDD16"}),(0,d.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"No experts created yet"}),(0,d.jsx)("p",{className:"text-sm",children:"Create your first AI expert to get started!"})]})})]}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("h3",{className:"text-2xl font-bold text-gray-900",children:["Your AI Experts (",e.length,")"]}),c&&(0,d.jsxs)(q.$,{onClick:c,className:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl transition-all duration-200 hover:shadow-lg flex items-center gap-2",children:[(0,d.jsx)(t.A,{className:"w-5 h-5"}),"Create Expert"]})]}),(0,d.jsx)("div",{className:"grid gap-6",children:e.map(b=>{let c=p[b.id];return(0,d.jsx)(r.Zp,{className:"bg-white shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-200",children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[b.imageUrl?(0,d.jsx)(h.default,{src:(0,o.L)(b.imageUrl),alt:b.name,width:64,height:64,className:"w-16 h-16 object-cover rounded-full border-3 border-white shadow-lg"}):(0,d.jsx)("div",{className:"w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl shadow-lg",style:{backgroundColor:"#1E3A8A"},children:O(b.labels)}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,d.jsx)("h4",{className:"text-xl font-bold text-gray-900",children:b.name}),(0,d.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium",children:b.model}),b.isPublic&&(0,d.jsx)("span",{className:"text-xs bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium",children:"Public"})]}),b.description&&(0,d.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:b.description}),b.labels&&b.labels.length>0&&(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:b.labels.map((a,b)=>(0,d.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-md",children:a},b))})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)(q.$,{onClick:()=>a?.(b),variant:"outline",size:"sm",className:"border-orange-200 text-orange-700 hover:bg-orange-50",children:[(0,d.jsx)(u.A,{className:"w-4 h-4 mr-1"}),"Edit"]}),(0,d.jsx)(F(),{href:`/expert/${b.id}`,children:(0,d.jsxs)(q.$,{size:"sm",className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,d.jsx)(v.A,{className:"w-4 h-4 mr-1"}),"View"]})})]})]}),c&&(0,d.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 mb-4",children:[(0,d.jsxs)("h5",{className:"font-semibold text-gray-900 mb-3 flex items-center",children:[(0,d.jsx)(w.A,{className:"w-4 h-4 mr-2 text-blue-600"}),"Performance Stats"]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,d.jsx)(x.A,{className:"w-4 h-4 text-blue-600 mr-1"}),(0,d.jsx)("span",{className:"text-2xl font-bold text-blue-600",children:c.totalUsers})]}),(0,d.jsx)("div",{className:"text-xs text-gray-600",children:"Total Users"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,d.jsx)(y.A,{className:"w-4 h-4 text-green-600 mr-1"}),(0,d.jsx)("span",{className:"text-2xl font-bold text-green-600",children:M(c.totalCommission).replace("IDR","").trim()})]}),(0,d.jsx)("div",{className:"text-xs text-gray-600",children:"Total Commission"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,d.jsx)(z.A,{className:"w-4 h-4 text-orange-600 mr-1"}),(0,d.jsx)("span",{className:"text-2xl font-bold text-orange-600",children:M(c.last30DaysCommission).replace("IDR","").trim()})]}),(0,d.jsx)("div",{className:"text-xs text-gray-600",children:"Last 30 Days"})]}),(0,d.jsx)("div",{className:"text-center",children:(0,d.jsxs)(q.$,{onClick:()=>(a=>{let b=e.find(b=>b.id===a);b&&(J(b),H(!0))})(b.id),size:"sm",variant:"outline",className:"w-full border-purple-200 text-purple-700 hover:bg-purple-50",children:[(0,d.jsx)(A,{className:"w-4 h-4 mr-1"}),"Report"]})})]})]}),(0,d.jsx)("div",{className:"text-xs text-gray-500 space-y-1 border-t border-gray-100 pt-4",children:(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsxs)("span",{children:["Pricing: ",b.pricingPercentage,"% commission"]}),(0,d.jsxs)("span",{children:["Created: ",N(b.createdAt)]})]})})]})},b.id)})}),(0,d.jsx)(s.lG,{open:G,onOpenChange:H,children:(0,d.jsxs)(s.Cf,{className:"md:max-w-4xl w-screen max-w-[calc(100%-2rem)] max-h-[90vh] overflow-y-auto",children:[(0,d.jsxs)(s.c7,{children:[(0,d.jsxs)(s.L3,{className:"flex items-center gap-3 text-xl",children:[(0,d.jsx)(B.A,{className:"w-6 h-6 text-blue-600"}),"Commission Report - ",I?.name]}),(0,d.jsx)(s.rr,{children:"Detailed commission and performance analytics for your AI expert"})]}),I&&(0,d.jsxs)("div",{className:"space-y-6 mt-6",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6",children:(0,d.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[I.imageUrl?(0,d.jsx)(h.default,{src:(0,o.L)(I.imageUrl),alt:I.name,width:64,height:64,className:"w-16 h-16 object-cover rounded-full border-3 border-white shadow-lg"}):(0,d.jsx)("div",{className:"w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl shadow-lg",style:{backgroundColor:"#1E3A8A"},children:O(I.labels)}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:I.name}),(0,d.jsx)("p",{className:"text-gray-600",children:I.description}),(0,d.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,d.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:I.model}),(0,d.jsxs)("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:[I.pricingPercentage,"% Commission"]})]})]})]})}),p[I.id]&&(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)(r.Zp,{className:"p-6 border-green-200 bg-green-50",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h4",{className:"font-semibold text-green-800",children:"Total Commission"}),(0,d.jsx)(y.A,{className:"w-6 h-6 text-green-600"})]}),(0,d.jsx)("p",{className:"text-3xl font-bold text-green-700",children:M(p[I.id].totalCommission)}),(0,d.jsx)("p",{className:"text-sm text-green-600 mt-1",children:"All time earnings"})]}),(0,d.jsxs)(r.Zp,{className:"p-6 border-orange-200 bg-orange-50",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h4",{className:"font-semibold text-orange-800",children:"Last 30 Days"}),(0,d.jsx)(z.A,{className:"w-6 h-6 text-orange-600"})]}),(0,d.jsx)("p",{className:"text-3xl font-bold text-orange-700",children:M(p[I.id].last30DaysCommission)}),(0,d.jsx)("p",{className:"text-sm text-orange-600 mt-1",children:"Recent performance"})]}),(0,d.jsxs)(r.Zp,{className:"p-6 border-blue-200 bg-blue-50",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h4",{className:"font-semibold text-blue-800",children:"Active Users"}),(0,d.jsx)(x.A,{className:"w-6 h-6 text-blue-600"})]}),(0,d.jsx)("p",{className:"text-3xl font-bold text-blue-700",children:p[I.id].totalUsers}),(0,d.jsx)("p",{className:"text-sm text-blue-600 mt-1",children:"Total users served"})]})]}),(0,d.jsxs)(r.Zp,{className:"p-6",children:[(0,d.jsxs)("h4",{className:"font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,d.jsx)(C,{className:"w-5 h-5 text-purple-600"}),"Performance Breakdown"]}),p[I.id]&&(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("span",{className:"text-gray-700 font-medium",children:"Total Sessions"}),(0,d.jsx)("span",{className:"text-xl font-bold text-gray-900",children:p[I.id].totalSessions})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("span",{className:"text-gray-700 font-medium",children:"Total Messages"}),(0,d.jsx)("span",{className:"text-xl font-bold text-gray-900",children:p[I.id].totalMessages})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("span",{className:"text-gray-700 font-medium",children:"Avg. Messages/Session"}),(0,d.jsx)("span",{className:"text-xl font-bold text-gray-900",children:p[I.id].totalSessions>0?Math.round(p[I.id].totalMessages/p[I.id].totalSessions):0})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("span",{className:"text-gray-700 font-medium",children:"Commission Rate"}),(0,d.jsxs)("span",{className:"text-xl font-bold text-gray-900",children:[I.pricingPercentage,"%"]})]})]})]})]}),(0,d.jsxs)(r.Zp,{className:"p-6",children:[(0,d.jsxs)("h4",{className:"font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,d.jsx)(D.A,{className:"w-5 h-5 text-indigo-600"}),"Expert Information"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Created Date"}),(0,d.jsx)("p",{className:"text-gray-900",children:N(I.createdAt)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Model"}),(0,d.jsx)("p",{className:"text-gray-900",children:I.model})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Visibility"}),(0,d.jsx)("p",{className:"text-gray-900",children:I.isPublic?"Public":"Private"})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[I.labels&&I.labels.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Labels"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:I.labels.map((a,b)=>(0,d.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-md",children:a},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Last Updated"}),(0,d.jsx)("p",{className:"text-gray-900",children:N(I.updatedAt)})]})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200",children:[(0,d.jsx)(q.$,{variant:"outline",onClick:()=>H(!1),className:"px-6",children:"Close"}),(0,d.jsxs)(q.$,{onClick:()=>{H(!1),a?.(I)},className:"px-6 bg-blue-600 hover:bg-blue-700 text-white",children:[(0,d.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Edit Expert"]})]})]})]})})]})};var H=c(78200);let I=(0,k.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var J=c(64398);let K=(0,k.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),L=(0,k.A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]),M=(0,k.A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),N=(0,k.A)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]]),O=()=>{let[a,b]=(0,f.useState)(null),[c,e]=(0,f.useState)([]),[g,h]=(0,f.useState)([]),[j,k]=(0,f.useState)(!0),[l,m]=(0,f.useState)(null);(0,f.useEffect)(()=>{n()},[]);let n=async()=>{try{k(!0),m(null);let a=await i.FH.listExperts();if(a.success){let c=a.experts,d={totalExperts:c.length,publicExperts:c.filter(a=>a.isPublic).length,totalUsers:0,totalRevenue:0,totalCommission:0,last30DaysCommission:0,totalSessions:0,totalMessages:0,averageRating:0,topPerformingExpert:c[0]?.name||"N/A"},f=[],g={};if(await Promise.all(c.slice(0,10).map(async a=>{try{let b=await i.FH.getExpertStats(a.id.toString());if(b.success){let c=b.stats;d.totalUsers+=c.totalUsers,d.totalRevenue+=c.totalRevenue||0,d.totalCommission+=c.totalCommission,d.last30DaysCommission+=c.last30DaysCommission,d.totalSessions+=c.totalSessions,d.totalMessages+=c.totalMessages,f.push({id:a.id,name:a.name,totalUsers:c.totalUsers,totalCommission:c.totalCommission,last30DaysCommission:c.last30DaysCommission});let e=new Date(a.createdAt).toLocaleDateString("en-US",{month:"short"});g[e]||(g[e]={revenue:0,commission:0,count:0}),g[e].revenue+=c.totalRevenue||0,g[e].commission+=c.totalCommission,g[e].count+=1}}catch(b){console.error(`Failed to load stats for expert ${a.id}:`,b)}})),f.sort((a,b)=>b.totalCommission-a.totalCommission),f.length>0&&(d.topPerformingExpert=f[0].name),f.length>0){let a=f.reduce((a,b)=>{let c=d.totalUsers/f.length,e=Math.min(5,Math.max(1,b.totalUsers/c*4+1));return a+e},0);d.averageRating=Number((a/f.length).toFixed(1))}else d.averageRating=0;let j=[],k=new Date;for(let a=5;a>=0;a--){let b=new Date(k.getFullYear(),k.getMonth()-a,1).toLocaleDateString("en-US",{month:"short"}),c=g[b]||{revenue:0,commission:0,count:0},d=0;if(a<5){let b=g[new Date(k.getFullYear(),k.getMonth()-a-1,1).toLocaleDateString("en-US",{month:"short"})]||{revenue:0,commission:0,count:0};b.revenue>0&&(d=Math.round((c.revenue-b.revenue)/b.revenue*100))}j.push({month:b,revenue:c.revenue,commission:c.commission,growth:d})}b(d),e(f.slice(0,5)),h(j)}else m(a.error||"Failed to load overview data")}catch(a){m(a.message||"Failed to load overview data")}finally{k(!1)}},o=a=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0,maximumFractionDigits:0}).format(a),p=a=>new Intl.NumberFormat("en-US").format(a);return j?(0,d.jsx)(r.Zp,{className:"p-8",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsx)("div",{className:"h-24 bg-gray-200 rounded"},b))})]})}):l?(0,d.jsx)(r.Zp,{className:"p-6 border-red-200 bg-red-50",children:(0,d.jsxs)("div",{className:"text-red-700",children:[(0,d.jsx)("p",{className:"font-semibold mb-2",children:"Error loading overview"}),(0,d.jsx)("p",{className:"text-sm mb-4",children:l}),(0,d.jsx)(q.$,{onClick:n,variant:"outline",className:"border-red-300 text-red-700 hover:bg-red-100",children:"Retry"})]})}):a?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"Expert Overview"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"Performance dashboard and insights"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(z.A,{className:"w-4 h-4 text-gray-500"}),(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:["Updated: ",new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,d.jsx)(r.Zp,{className:"p-6 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-blue-600 text-sm font-medium",children:"Total Experts"}),(0,d.jsx)("p",{className:"text-3xl font-bold text-blue-900",children:a.totalExperts}),(0,d.jsxs)("p",{className:"text-xs text-blue-700 mt-1",children:[a.publicExperts," public"]})]}),(0,d.jsx)(H.A,{className:"w-12 h-12 text-blue-600"})]})}),(0,d.jsx)(r.Zp,{className:"p-6 bg-gradient-to-br from-green-50 to-green-100 border-green-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-green-600 text-sm font-medium",children:"Total Commission"}),(0,d.jsx)("p",{className:"text-3xl font-bold text-green-900",children:o(a.totalCommission).replace("IDR","").trim()}),(0,d.jsxs)("p",{className:"text-xs text-green-700 mt-1",children:["Last 30 days: ",o(a.last30DaysCommission).replace("IDR","").trim()]})]}),(0,d.jsx)(y.A,{className:"w-12 h-12 text-green-600"})]})}),(0,d.jsx)(r.Zp,{className:"p-6 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-purple-600 text-sm font-medium",children:"Total Users"}),(0,d.jsx)("p",{className:"text-3xl font-bold text-purple-900",children:p(a.totalUsers)}),(0,d.jsx)("p",{className:"text-xs text-purple-700 mt-1",children:"Across all experts"})]}),(0,d.jsx)(x.A,{className:"w-12 h-12 text-purple-600"})]})}),(0,d.jsx)(r.Zp,{className:"p-6 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-orange-600 text-sm font-medium",children:"Total Sessions"}),(0,d.jsx)("p",{className:"text-3xl font-bold text-orange-900",children:p(a.totalSessions)}),(0,d.jsxs)("p",{className:"text-xs text-orange-700 mt-1",children:[p(a.totalMessages)," messages"]})]}),(0,d.jsx)(I,{className:"w-12 h-12 text-orange-600"})]})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,d.jsxs)(r.Zp,{className:"lg:col-span-2 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("h3",{className:"text-xl font-bold text-gray-900 flex items-center",children:[(0,d.jsx)(C,{className:"w-5 h-5 mr-2 text-blue-600"}),"Top Performing Experts"]}),(0,d.jsxs)(q.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(v.A,{className:"w-4 h-4 mr-1"}),"View All"]})]}),(0,d.jsx)("div",{className:"space-y-4",children:c.length>0?c.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-bold",children:b+1}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-semibold text-gray-900",children:a.name}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[a.totalUsers," users"]})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("p",{className:"font-bold text-green-600",children:o(a.totalCommission).replace("IDR","").trim()}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:["30d: ",o(a.last30DaysCommission).replace("IDR","").trim()]})]})]},a.id)):(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)(H.A,{className:"w-12 h-12 mx-auto mb-4 text-gray-300"}),(0,d.jsx)("p",{children:"No expert performance data available yet"})]})})]}),(0,d.jsxs)(r.Zp,{className:"p-6",children:[(0,d.jsxs)("h3",{className:"text-xl font-bold text-gray-900 mb-6 flex items-center",children:[(0,d.jsx)(B.A,{className:"w-5 h-5 mr-2 text-purple-600"}),"Quick Stats"]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,d.jsx)(J.A,{className:"w-5 h-5 text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-semibold text-gray-900",children:"Avg. Rating"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"User satisfaction"})]})]}),(0,d.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:a.averageRating})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:(0,d.jsx)(w.A,{className:"w-5 h-5 text-green-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-semibold text-gray-900",children:"Growth Rate"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"This month"})]})]}),(0,d.jsx)("p",{className:"text-2xl font-bold text-green-600",children:g.length>0&&g[g.length-1].growth>0?`+${g[g.length-1].growth}%`:g.length>0?`${g[g.length-1].growth}%`:"0%"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,d.jsx)(K,{className:"w-5 h-5 text-purple-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-semibold text-gray-900",children:"Top Expert"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Best performer"})]})]}),(0,d.jsx)("p",{className:"text-sm font-bold text-purple-600 text-right max-w-24 truncate",children:a.topPerformingExpert})]}),(0,d.jsx)("div",{className:"pt-4 border-t border-gray-200",children:(0,d.jsxs)(q.$,{className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",onClick:()=>window.open("/analytics","_blank"),children:[(0,d.jsx)(L,{className:"w-4 h-4 mr-2"}),"View Detailed Analytics"]})})]})]})]}),(0,d.jsxs)(r.Zp,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("h3",{className:"text-xl font-bold text-gray-900 flex items-center",children:[(0,d.jsx)(w.A,{className:"w-5 h-5 mr-2 text-green-600"}),"Revenue Trend Analysis"]}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:(0,d.jsxs)(q.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(z.A,{className:"w-4 h-4 mr-1"}),"Last 6 Months"]})})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{className:"text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,d.jsx)(y.A,{className:"w-5 h-5 text-blue-600 mr-1"}),(0,d.jsx)("span",{className:"text-sm text-blue-600 font-medium",children:"Total Revenue"})]}),(0,d.jsx)("p",{className:"text-2xl font-bold text-blue-900",children:o(a.totalRevenue)}),(0,d.jsx)("p",{className:"text-xs text-blue-700 mt-1",children:"All time earnings"})]}),(0,d.jsxs)("div",{className:"text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,d.jsx)(C,{className:"w-5 h-5 text-green-600 mr-1"}),(0,d.jsx)("span",{className:"text-sm text-green-600 font-medium",children:"Your Commission"})]}),(0,d.jsx)("p",{className:"text-2xl font-bold text-green-900",children:o(a.totalCommission)}),(0,d.jsxs)("p",{className:"text-xs text-green-700 mt-1",children:[(a.totalCommission/Math.max(a.totalRevenue,1)*100).toFixed(1),"% of total"]})]}),(0,d.jsxs)("div",{className:"text-center p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg border border-orange-200",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,d.jsx)(z.A,{className:"w-5 h-5 text-orange-600 mr-1"}),(0,d.jsx)("span",{className:"text-sm text-orange-600 font-medium",children:"Last 30 Days"})]}),(0,d.jsx)("p",{className:"text-2xl font-bold text-orange-900",children:o(a.last30DaysCommission)}),(0,d.jsx)("p",{className:"text-xs text-orange-700 mt-1",children:"Recent commission"})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900",children:"Monthly Revenue & Commission"}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full mr-2"}),(0,d.jsx)("span",{className:"text-gray-600",children:"Revenue"})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full mr-2"}),(0,d.jsx)("span",{className:"text-gray-600",children:"Commission"})]})]})]}),(0,d.jsx)("div",{className:"grid grid-cols-6 gap-4 items-end h-48",children:g.map(a=>{let b=Math.max(...g.map(a=>a.revenue)),c=a.revenue/b*100,e=a.commission/b*100;return(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-end space-x-1 h-32",children:[(0,d.jsxs)("div",{className:"relative group",children:[(0,d.jsx)("div",{className:"w-6 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-md transition-all duration-300 hover:from-blue-600 hover:to-blue-500",style:{height:`${c}%`}}),(0,d.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,d.jsx)("div",{className:"bg-gray-900 text-white text-xs rounded px-2 py-1 whitespace-nowrap",children:o(a.revenue)})})]}),(0,d.jsxs)("div",{className:"relative group",children:[(0,d.jsx)("div",{className:"w-6 bg-gradient-to-t from-green-500 to-green-400 rounded-t-md transition-all duration-300 hover:from-green-600 hover:to-green-500",style:{height:`${e}%`}}),(0,d.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,d.jsx)("div",{className:"bg-gray-900 text-white text-xs rounded px-2 py-1 whitespace-nowrap",children:o(a.commission)})})]})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-700",children:a.month}),(0,d.jsx)("div",{className:"flex items-center justify-center mt-1",children:a.growth>0?(0,d.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,d.jsx)(M,{className:"w-3 h-3"}),(0,d.jsxs)("span",{className:"text-xs font-medium",children:[a.growth,"%"]})]}):a.growth<0?(0,d.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,d.jsx)(N,{className:"w-3 h-3"}),(0,d.jsxs)("span",{className:"text-xs font-medium",children:[Math.abs(a.growth),"%"]})]}):(0,d.jsx)("div",{className:"flex items-center text-gray-500",children:(0,d.jsx)("span",{className:"text-xs font-medium",children:"0%"})})})]})]},a.month)})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsx)(r.Zp,{className:"p-4 border-l-4 border-green-500 bg-green-50",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Best Month"}),(0,d.jsx)("p",{className:"text-lg font-bold text-green-900",children:g.length>0&&g.reduce((a,b)=>a.commission>b.commission?a:b).month})]}),(0,d.jsx)(w.A,{className:"w-8 h-8 text-green-600"})]})}),(0,d.jsx)(r.Zp,{className:"p-4 border-l-4 border-blue-500 bg-blue-50",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Avg. Growth"}),(0,d.jsxs)("p",{className:"text-lg font-bold text-blue-900",children:[g.length>0&&Math.round(g.reduce((a,b)=>a+b.growth,0)/g.length),"%"]})]}),(0,d.jsx)(B.A,{className:"w-8 h-8 text-blue-600"})]})}),(0,d.jsx)(r.Zp,{className:"p-4 border-l-4 border-purple-500 bg-purple-50",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-purple-800",children:"Avg. Commission Rate"}),(0,d.jsx)("p",{className:"text-lg font-bold text-purple-900",children:a.totalRevenue>0?`${(a.totalCommission/a.totalRevenue*100).toFixed(1)}%`:"0%"})]}),(0,d.jsx)(C,{className:"w-8 h-8 text-purple-600"})]})})]})]})]})]}):null};var P=c(11860);let Q=({view:a="overview"})=>{let[b,c]=(0,f.useState)(!1),[e,g]=(0,f.useState)(!1),[h,i]=(0,f.useState)(null),[j,k]=(0,f.useState)(0),l=()=>{i(null),g(!1)};return(0,d.jsxs)("div",{className:"max-w-7xl mx-auto p-6",children:["overview"===a?(0,d.jsx)(O,{}):(0,d.jsx)(G,{onExpertSelect:a=>{console.log("Expert selected:",a)},onExpertEdit:a=>{console.log("Expert edit:",a),i(a),g(!0)},refreshTrigger:j,onCreateExpert:()=>c(!0)}),b&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Create New Expert"}),(0,d.jsx)("button",{onClick:()=>c(!1),className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,d.jsx)(P.A,{className:"w-6 h-6"})})]}),(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)(n,{onExpertCreated:a=>{console.log("Expert created:",a),k(a=>a+1),c(!1)},onCancel:()=>c(!1)})})]})}),e&&h&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,d.jsxs)("h2",{className:"text-2xl font-bold text-gray-900",children:["Edit Expert: ",h.name]}),(0,d.jsx)("button",{onClick:l,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,d.jsx)(P.A,{className:"w-6 h-6"})})]}),(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)(p,{expert:h,onExpertUpdated:a=>{console.log("Expert updated:",a),k(a=>a+1),g(!1),i(null)},onCancel:l})})]})})]})};function R(){let a=(0,e.useSearchParams)().get("view")||"overview";return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"overview"===a?"Expert Overview":"Manage AI Experts"}),(0,d.jsx)("p",{className:"text-gray-600",children:"overview"===a?"View comprehensive statistics and performance metrics for your AI experts":"Create and manage your AI experts"})]}),(0,d.jsx)(Q,{view:a})]})})}function S(){return(0,d.jsx)(f.Suspense,{fallback:(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,d.jsx)(R,{})})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},23928:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41862:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},43637:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["experts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,63542)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\experts\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\experts\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/experts/page",pathname:"/experts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/experts/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},53411:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},56085:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(a,b,c)=>{"use strict";c.d(b,{Cf:()=>l,L3:()=>n,c7:()=>m,lG:()=>h,rr:()=>o,zM:()=>i});var d=c(60687);c(43210);var e=c(80882),f=c(11860),g=c(4780);function h({...a}){return(0,d.jsx)(e.bL,{"data-slot":"dialog",...a})}function i({...a}){return(0,d.jsx)(e.l9,{"data-slot":"dialog-trigger",...a})}function j({...a}){return(0,d.jsx)(e.ZL,{"data-slot":"dialog-portal",...a})}function k({className:a,...b}){return(0,d.jsx)(e.hJ,{"data-slot":"dialog-overlay",className:(0,g.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function l({className:a,children:b,showCloseButton:c=!0,...h}){return(0,d.jsxs)(j,{"data-slot":"dialog-portal",children:[(0,d.jsx)(k,{}),(0,d.jsxs)(e.UC,{"data-slot":"dialog-content",className:(0,g.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...h,children:[b,c&&(0,d.jsxs)(e.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,d.jsx)(f.A,{}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"dialog-header",className:(0,g.cn)("flex flex-col gap-2 text-center sm:text-left",a),...b})}function n({className:a,...b}){return(0,d.jsx)(e.hE,{"data-slot":"dialog-title",className:(0,g.cn)("text-lg leading-none font-semibold",a),...b})}function o({className:a,...b}){return(0,d.jsx)(e.VY,{"data-slot":"dialog-description",className:(0,g.cn)("text-muted-foreground text-sm",a),...b})}},63542:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\Web\\\\pakarai\\\\ai-trainer\\\\fe\\\\src\\\\app\\\\experts\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\experts\\page.tsx","default")},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},74075:a=>{"use strict";a.exports=require("zlib")},78200:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86328:(a,b,c)=>{Promise.resolve().then(c.bind(c,63542))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96056:(a,b,c)=>{Promise.resolve().then(c.bind(c,9718))},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,842,928,235,474,263],()=>b(b.s=43637));module.exports=c})();