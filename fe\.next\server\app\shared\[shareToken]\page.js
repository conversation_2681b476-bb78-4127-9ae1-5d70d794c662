(()=>{var a={};a.id=627,a.ids=[627],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9424:(a,b,c)=>{Promise.resolve().then(c.bind(c,93235))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},25334:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29867:(a,b,c)=>{"use strict";c.d(b,{d:()=>i});var d=c(43210);let e={toasts:[]},f=[];function g(){f.forEach(a=>a(e))}function h(a){let b=e.toasts.findIndex(b=>b.id===a);b>-1&&(e.toasts.splice(b,1),g())}function i(){let[a,b]=(0,d.useState)(e),c=(0,d.useCallback)(a=>(f.push(a),()=>{f=f.filter(b=>b!==a)}),[]),i=(0,d.useCallback)(a=>(function(a){let b=Math.random().toString(36).substr(2,9),c={id:b,duration:5e3,...a};return e.toasts.push(c),g(),c.duration&&c.duration>0&&setTimeout(()=>{h(b)},c.duration),b})(a),[]),j=(0,d.useCallback)(a=>{h(a)},[]);return(0,d.useState)(()=>c(b)),{toast:i,dismiss:j,toasts:a.toasts}}},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},51280:(a,b,c)=>{Promise.resolve().then(c.bind(c,69109))},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69109:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\Web\\\\pakarai\\\\ai-trainer\\\\fe\\\\src\\\\app\\\\shared\\\\[shareToken]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\shared\\[shareToken]\\page.tsx","default")},70334:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},72085:(a,b,c)=>{"use strict";c.d(b,{E:()=>g,_:()=>h});var d=c(43210),e=c(29867),f=c(62185);function g(){let{toast:a}=(0,e.d)(),[b,c]=(0,d.useState)(!1),[g,h]=(0,d.useState)(null),i=(0,d.useCallback)(async b=>{try{c(!0),h(null);let d=await f.FH.post("/sharing/shares",{body:b,headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(d.data.success){let b=d.data.data,c=`${window.location.origin}/shared/${b.shareToken}`;return a({title:"Share Created!",description:"Your expert share link has been created successfully."}),{...b,shareUrl:c}}throw Error(d.data.message||"Failed to create share")}catch(c){let b=c.response?.data?.message||c.message||"Failed to create share";return h(b),a({title:"Error",description:b,variant:"destructive"}),null}finally{c(!1)}},[a]),j=(0,d.useCallback)(async b=>{try{c(!0),h(null);let a=await f.FH.get(`/sharing/shared/${b}`);if(a.data.success)return a.data.data;throw Error(a.data.message||"Failed to load shared expert")}catch(c){let b=c.response?.data?.message||c.message||"Failed to load shared expert";return h(b),c.response?.status!==404&&a({title:"Error",description:b,variant:"destructive"}),null}finally{c(!1)}},[a]),k=(0,d.useCallback)(async(a,b)=>{try{return(await f.FH.post(`/sharing/track/${a}/click`,{body:{metadata:{userAgent:navigator.userAgent,referrer:document.referrer,timestamp:new Date().toISOString(),...b}}})).data.success}catch(a){return console.error("Failed to track click:",a),!1}},[]),l=(0,d.useCallback)(async(a,b)=>{try{return(await f.FH.post(`/sharing/track/${a}/conversion`,{body:{metadata:{timestamp:new Date().toISOString(),...b}},headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).data.success}catch(a){return console.error("Failed to track conversion:",a),!1}},[]),m=(0,d.useCallback)(async a=>{try{let b=await f.FH.get(`/sharing/consent/${a}`);if(b.data.success)return b.data.data;return null}catch(a){return console.error("Failed to get consent:",a),null}},[]),n=(0,d.useCallback)(async(b,c,d=!0)=>{try{if((await f.FH.post(`/sharing/consent/${b}`,{body:{consent:c,trackingEnabled:d}})).data.success)return a({title:c?"Consent Granted":"Consent Withdrawn",description:c?"Thank you for allowing us to improve your experience.":"Your privacy preferences have been updated."}),!0;return!1}catch(b){return console.error("Failed to set consent:",b),a({title:"Error",description:"Failed to update consent preferences.",variant:"destructive"}),!1}},[a]),o=(0,d.useCallback)(async(b,d)=>{try{c(!0),h(null);let e=await f.FH.put(`/sharing/shares/${b}`,{body:d,headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(e.data.success)return a({title:"Share Updated",description:"Your share settings have been updated successfully."}),!0;throw Error(e.data.message||"Failed to update share")}catch(c){let b=c.response?.data?.message||c.message||"Failed to update share";return h(b),a({title:"Error",description:b,variant:"destructive"}),!1}finally{c(!1)}},[a]),p=(0,d.useCallback)(async b=>{try{c(!0),h(null);let d=await f.FH.delete(`/sharing/shares/${b}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(d.data.success)return a({title:"Share Deleted",description:"The share link has been deactivated successfully."}),!0;throw Error(d.data.message||"Failed to delete share")}catch(c){let b=c.response?.data?.message||c.message||"Failed to delete share";return h(b),a({title:"Error",description:b,variant:"destructive"}),!1}finally{c(!1)}},[a]),q=(0,d.useCallback)(async()=>{try{c(!0),h(null);let a=await f.FH.get("/sharing/shares/my",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(a.data.success)return a.data.data;throw Error(a.data.message||"Failed to load shares")}catch(c){let b=c.response?.data?.message||c.message||"Failed to load shares";return h(b),a({title:"Error",description:b,variant:"destructive"}),null}finally{c(!1)}},[a]),r=(0,d.useCallback)(async(b,d="7d")=>{try{c(!0),h(null);let a=await f.FH.get(`/sharing/analytics/${b}?timeRange=${d}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(a.data.success)return a.data.data;throw Error(a.data.message||"Failed to load analytics")}catch(c){let b=c.response?.data?.message||c.message||"Failed to load analytics";return h(b),a({title:"Error",description:b,variant:"destructive"}),null}finally{c(!1)}},[a]),s=(0,d.useCallback)(async b=>{try{let c=`${window.location.origin}/shared/${b}`;return await navigator.clipboard.writeText(c),a({title:"Copied!",description:"Share link copied to clipboard."}),!0}catch(b){return console.error("Failed to copy to clipboard:",b),a({title:"Error",description:"Failed to copy link. Please copy manually.",variant:"destructive"}),!1}},[a]);return{loading:b,error:g,createShare:i,getSharedExpert:j,trackClick:k,trackConversion:l,getConsent:m,setConsent:n,updateShare:o,deleteShare:p,getUserShares:q,getAnalytics:r,copyShareUrl:s,generateShareUrl:(0,d.useCallback)(a=>`${window.location.origin}/shared/${a}`,[]),clearError:(0,d.useCallback)(()=>{h(null)},[])}}function h(a){let[b,c]=(0,d.useState)(a||null),[e,f]=(0,d.useState)(null),[h,i]=(0,d.useState)(null),j=g(),k=(0,d.useCallback)(async a=>{c(a);let b=await j.getSharedExpert(a);return f(b),b},[j]);return{shareToken:b,shareData:e,consentData:h,loadSharedExpert:k,loadConsent:(0,d.useCallback)(async a=>{let b=await j.getConsent(a);return i(b),b},[j]),handleConsent:(0,d.useCallback)(async(a,b,c=!0)=>{let d=await j.setConsent(a,b,c);return d&&i({hasConsented:b,consentTimestamp:new Date().toISOString(),trackingEnabled:c}),d},[j]),...j}}},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81620:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},81630:a=>{"use strict";a.exports=require("http")},83771:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["shared",{children:["[shareToken]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,69109)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\shared\\[shareToken]\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\shared\\[shareToken]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/shared/[shareToken]/page",pathname:"/shared/[shareToken]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/shared/[shareToken]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},93235:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>y});var d=c(60687),e=c(43210),f=c(16189),g=c(29523),h=c(44493),i=c(96834),j=c(12720),k=c(41862),l=c(93613),m=c(81620),n=c(25334);let o=(0,c(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]);var p=c(41312),q=c(64398),r=c(33872),s=c(70334),t=c(29867),u=c(72085),v=c(85814),w=c.n(v);function x(){let a=(0,f.useParams)().shareToken,b=(0,f.useRouter)(),{toast:c}=(0,t.d)(),{loading:v,error:x,shareData:y,loadSharedExpert:z,loadConsent:A,trackClick:B,trackConversion:C}=(0,u._)(a),[D,E]=(0,e.useState)(!1),[F,G]=(0,e.useState)(!1),H=async()=>{if(y)try{if(G(!0),!localStorage.getItem("token")){let a=encodeURIComponent(window.location.href);b.push(`/auth/login?returnUrl=${a}`);return}await C(a,{expertId:y.expert.id,source:"shared_link"}),b.push(`/chat/${y.expert.id}?source=shared&shareToken=${a}`)}catch(a){console.error("Error starting chat:",a),c({title:"Error",description:"Failed to start chat. Please try again.",variant:"destructive"})}finally{G(!1)}},I=async()=>{try{let a=window.location.href;await navigator.clipboard.writeText(a),c({title:"Link Copied!",description:"Share link copied to clipboard."})}catch{c({title:"Error",description:"Failed to copy link. Please copy manually.",variant:"destructive"})}};if(v)return(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(k.A,{className:"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Loading Expert..."}),(0,d.jsx)("p",{className:"text-gray-600",children:"Please wait while we prepare your AI expert."})]})});if(x||!y)return(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center max-w-md mx-auto p-6",children:[(0,d.jsx)(l.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Expert Not Found"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:x||"The shared expert link you're looking for doesn't exist or has been deactivated."}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(g.$,{onClick:()=>b.push("/ai-experts"),className:"w-full",children:"Browse All Experts"}),(0,d.jsx)(g.$,{variant:"outline",onClick:()=>b.push("/"),className:"w-full",children:"Go to Homepage"})]})]})});let{expert:J,share:K}=y;return K.isActive?(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50",children:[(0,d.jsx)("div",{className:"bg-white/80 backdrop-blur-sm border-b sticky top-0 z-10",children:(0,d.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(m.A,{className:"h-6 w-6 text-blue-600"}),(0,d.jsx)("span",{className:"font-semibold text-gray-900",children:"Shared AI Expert"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)(g.$,{variant:"outline",size:"sm",onClick:I,children:[(0,d.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Share"]}),(0,d.jsx)(g.$,{variant:"outline",size:"sm",asChild:!0,children:(0,d.jsxs)(w(),{href:"/ai-experts",children:[(0,d.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Browse More"]})})]})]})})}),(0,d.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,d.jsx)(h.Zp,{className:"border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:(0,d.jsx)(h.Wu,{className:"p-8",children:(0,d.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,d.jsxs)(j.eu,{className:"h-20 w-20 border-4 border-white shadow-lg",children:[(0,d.jsx)(j.BK,{src:J.imageUrl,alt:J.name}),(0,d.jsx)(j.q5,{className:"text-2xl font-bold bg-gradient-to-br from-blue-500 to-purple-600 text-white",children:J.name.charAt(0)})]}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:J.name}),(0,d.jsx)("p",{className:"text-lg text-gray-600 mb-4",children:J.description}),J.tags&&J.tags.length>0&&(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:J.tags.map((a,b)=>(0,d.jsxs)(i.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:[(0,d.jsx)(o,{className:"h-3 w-3 mr-1"}),a]},b))}),J.category&&(0,d.jsx)(i.E,{variant:"outline",className:"mb-4",children:J.category}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,d.jsx)(p.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:["Created by ",J.createdBy.displayName||J.createdBy.username]})]})]})]})})}),(0,d.jsxs)(h.Zp,{className:"border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[(0,d.jsx)(h.aR,{children:(0,d.jsxs)(h.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(q.A,{className:"h-5 w-5 text-yellow-500"}),(0,d.jsx)("span",{children:"Share Statistics"})]})}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:K.clickCount}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Total Views"})]}),(0,d.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-600",children:new Date(K.createdAt).toLocaleDateString()}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Shared On"})]})]})})]}),(0,d.jsxs)(h.Zp,{className:"border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)(h.ZB,{children:"What You Can Do"}),(0,d.jsx)(h.BT,{children:"This AI expert can help you with specialized knowledge and assistance."})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(r.A,{className:"h-5 w-5 text-blue-500 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:"Start a Conversation"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Chat directly with this AI expert to get personalized assistance."})]})]}),(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(q.A,{className:"h-5 w-5 text-yellow-500 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:"Get Expert Advice"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Receive specialized knowledge and insights tailored to your needs."})]})]}),(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(p.A,{className:"h-5 w-5 text-green-500 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:"Join the Community"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Discover more AI experts and expand your knowledge network."})]})]})]})})]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(h.Zp,{className:"border-0 shadow-lg bg-gradient-to-br from-blue-500 to-purple-600 text-white",children:(0,d.jsxs)(h.Wu,{className:"p-6 text-center",children:[(0,d.jsx)(r.A,{className:"h-12 w-12 mx-auto mb-4 opacity-90"}),(0,d.jsx)("h3",{className:"text-xl font-bold mb-2",children:"Start Chatting Now"}),(0,d.jsxs)("p",{className:"text-blue-100 mb-6 text-sm",children:["Begin your conversation with ",J.name," and get the help you need."]}),(0,d.jsx)(g.$,{onClick:H,disabled:F,className:"w-full bg-white text-blue-600 hover:bg-gray-100 font-semibold",size:"lg",children:F?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Starting Chat..."]}):(0,d.jsxs)(d.Fragment,{children:["Start Chat",(0,d.jsx)(s.A,{className:"h-4 w-4 ml-2"})]})})]})}),(0,d.jsxs)(h.Zp,{className:"border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[(0,d.jsx)(h.aR,{children:(0,d.jsx)(h.ZB,{className:"text-lg",children:"Share Information"})}),(0,d.jsx)(h.Wu,{className:"space-y-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Monitoring"}),(0,d.jsx)(i.E,{variant:K.monitorEnabled?"default":"secondary",children:K.monitorEnabled?"Enabled":"Disabled"})]})})]}),(0,d.jsxs)(h.Zp,{className:"border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)(h.ZB,{className:"text-lg",children:"Explore More"}),(0,d.jsx)(h.BT,{children:"Discover other AI experts that might interest you."})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(g.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,d.jsxs)(w(),{href:"/ai-experts",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Browse All Experts"]})}),(0,d.jsx)(g.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,d.jsxs)(w(),{href:"/auth/register",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Create Your Own Expert"]})})]})})]})]})]})}),(0,d.jsx)("div",{className:"bg-white/80 backdrop-blur-sm border-t mt-12",children:(0,d.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-6 text-center",children:(0,d.jsxs)("p",{className:"text-gray-600 text-sm",children:["Powered by ",(0,d.jsx)("span",{className:"font-semibold text-blue-600",children:"AI Trainer Hub"})," - Your marketplace for AI expertise"]})})})]}):(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center max-w-md mx-auto p-6",children:[(0,d.jsx)(l.A,{className:"h-16 w-16 text-gray-500 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Link Inactive"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"This shared expert link has been deactivated by the creator."}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(g.$,{onClick:()=>b.push("/ai-experts"),className:"w-full",children:"Browse Available Experts"}),(0,d.jsx)(g.$,{variant:"outline",onClick:()=>b.push("/"),className:"w-full",children:"Go to Homepage"})]})]})})}function y(){return(0,d.jsx)(x,{})}},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,842,928,263,530],()=>b(b.s=83771));module.exports=c})();