"use client";

import { useRouter } from "next/navigation";
import { MarketplacePage } from "@/components/marketplace";

const AIExpertsPage = () => {
  const router = useRouter();

  const handleChatClick = (expertId: number) => {
    router.push(`/chat?expert=${expertId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">AI Experts</h1>
          <p className="text-gray-600">
            Discover and connect with AI specialists
          </p>
        </div>

        <MarketplacePage onChatClick={handleChatClick} />
      </div>
    </div>
  );
};

export default AIExpertsPage;
