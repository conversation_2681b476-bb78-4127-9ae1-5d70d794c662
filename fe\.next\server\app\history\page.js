(()=>{var a={};a.id=429,a.ids=[429],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},23e3:(a,b,c)=>{Promise.resolve().then(c.bind(c,68859))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32487:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,metadata:()=>g});var d=c(37413),e=c(50802);function f(){return(0,d.jsx)(e.default,{})}let g={title:"Chat History - AI Trainer Hub",description:"View and manage your chat conversations with AI experts"}},33872:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},50802:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\Web\\\\pakarai\\\\ai-trainer\\\\fe\\\\src\\\\components\\\\ChatHistory.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ChatHistory.tsx","default")},52123:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,32487)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\history\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\history\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/history/page",pathname:"/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/history/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68859:(a,b,c)=>{"use strict";c.d(b,{default:()=>p});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(62185),i=c(62688);let j=(0,i.A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),k=(0,i.A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);var l=c(88233),m=c(33872),n=c(58869),o=c(48730);let p=()=>{let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)(!0),[i,p]=(0,e.useState)(null),[q,r]=(0,e.useState)(null),[s,t]=(0,e.useState)(""),u=async()=>{try{f(!0),p(null);let a=await h.FH.getUserChatSessions(50);a.success?b(a.sessions):p(a.error||"Failed to load chat sessions")}catch(a){p(a.message||"Failed to load chat sessions")}finally{f(!1)}};(0,e.useEffect)(()=>{u()},[]);let v=a=>{let b=new Date(a),c=new Date,d=Math.floor((c.getTime()-b.getTime())/36e5),e=Math.floor(d/24);return d<1?"Just now":d<24?`${d}h ago`:e<7?`${e}d ago`:b.toLocaleDateString("en-US",{month:"short",day:"numeric",year:b.getFullYear()!==c.getFullYear()?"numeric":void 0})},w=async(c,d)=>{try{(await h.FH.updateSessionTitle(c.toString(),d)).success&&(b(a.map(a=>a.id===c?{...a,session_title:d}:a)),r(null),t(""))}catch(a){console.error("Failed to update title:",a)}},x=async c=>{if(confirm("Are you sure you want to delete this chat session?"))try{(await h.FH.deleteSession(c.toString())).success&&b(a.filter(a=>a.id!==c))}catch(a){console.error("Failed to delete session:",a)}},y=()=>{r(null),t("")};return c?(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)(g(),{href:"/",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors",children:[(0,d.jsx)("span",{children:"←"}),(0,d.jsx)("span",{children:"Back to Home"})]})}),(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Chat History"}),(0,d.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100 animate-pulse",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:"h-5 bg-gray-200 rounded w-1/3"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},b))})]})}):i?(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)(g(),{href:"/",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors",children:[(0,d.jsx)("span",{children:"←"}),(0,d.jsx)("span",{children:"Back to Home"})]})}),(0,d.jsx)("div",{className:"text-center py-12",children:(0,d.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-8 max-w-md mx-auto",children:[(0,d.jsx)("div",{className:"text-red-600 text-6xl mb-4",children:"⚠️"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-red-800 mb-2",children:"Unable to Load Chat History"}),(0,d.jsx)("p",{className:"text-red-600 mb-4",children:i}),(0,d.jsx)("button",{onClick:u,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Try Again"})]})})]})}):(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)(g(),{href:"/",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors",children:[(0,d.jsx)("span",{children:"←"}),(0,d.jsx)("span",{children:"Back to Home"})]})}),(0,d.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Chat History"}),(0,d.jsxs)("div",{className:"text-sm text-gray-500",children:[a.length," conversation",1!==a.length?"s":""]})]}),0===a.length?(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCAC"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-700 mb-2",children:"No Chat History Yet"}),(0,d.jsx)("p",{className:"text-gray-500 mb-6",children:"Start a conversation with an AI expert to see it here!"}),(0,d.jsx)(g(),{href:"/",className:"inline-block px-6 py-3 rounded-xl text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5",style:{backgroundColor:"#1E3A8A"},children:"Browse Experts"})]}):(0,d.jsx)("div",{className:"space-y-4",children:a.map(a=>(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-200 group",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,d.jsxs)("div",{className:"flex-1",children:[q===a.id?(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"text",value:s,onChange:a=>t(a.target.value),className:"flex-1 px-3 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",onKeyDown:b=>{"Enter"===b.key?w(a.id,s):"Escape"===b.key&&y()},autoFocus:!0}),(0,d.jsx)("button",{onClick:()=>w(a.id,s),className:"px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm",children:"Save"}),(0,d.jsx)("button",{onClick:y,className:"px-3 py-1 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors text-sm",children:"Cancel"})]}):(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 group-hover:text-blue-900 transition-colors",children:a.session_title}),(0,d.jsx)("button",{onClick:()=>(a=>{r(a.id),t(a.session_title)})(a),className:"opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-blue-600 transition-all",children:(0,d.jsx)(j,{className:"w-4 h-4"})})]}),a.expert_name&&(0,d.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,d.jsx)("div",{className:"w-6 h-6 rounded-full flex items-center justify-center text-white text-xs",style:{backgroundColor:"#1E3A8A"},children:(0,d.jsx)(k,{className:"w-3 h-3"})}),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:["Expert: ",(0,d.jsx)("span",{className:"font-medium",children:a.expert_name})]}),(0,d.jsx)("span",{className:"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full",children:a.expert_model})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,d.jsx)(g(),{href:`/chat?threadId=${a.thread_id}`,className:"px-4 py-2 rounded-lg text-white font-medium transition-all duration-200 hover:shadow-md",style:{backgroundColor:"#1E3A8A"},children:"Continue"}),(0,d.jsx)("button",{onClick:()=>x(a.id),className:"p-2 text-gray-400 hover:text-red-600 transition-colors",children:(0,d.jsx)(l.A,{className:"w-4 h-4"})})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(m.A,{className:"w-4 h-4"}),(0,d.jsxs)("span",{children:[a.message_count," messages"]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(n.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:a.user_messages})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(k,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:a.assistant_messages})]}),a.total_cost>0&&(0,d.jsx)("div",{children:(0,d.jsxs)("span",{children:["� ",(a=>{let b=Math.round(a).toLocaleString("en-US");return`Rp ${b}`})(a.total_cost)]})})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(o.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:a.last_message_at?v(a.last_message_at):v(a.created_at)})]})]})]},a.id))})]})})}},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88152:(a,b,c)=>{Promise.resolve().then(c.bind(c,50802))},88233:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,842,263],()=>b(b.s=52123));module.exports=c})();