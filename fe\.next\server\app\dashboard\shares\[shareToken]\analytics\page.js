(()=>{var a={};a.id=820,a.ids=[820],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(a,b,c)=>{"use strict";c.d(b,{L:()=>h,cn:()=>g});var d=c(49384),e=c(82348);let f=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001";function g(...a){return(0,e.QP)((0,d.$)(a))}function h(a){return`${f}/${a.startsWith("/")?a.replace("/",""):a}`}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11273:(a,b,c)=>{"use strict";c.d(b,{A:()=>g,q:()=>f});var d=c(43210),e=c(60687);function f(a,b){let c=d.createContext(b),f=a=>{let{children:b,...f}=a,g=d.useMemo(()=>f,Object.values(f));return(0,e.jsx)(c.Provider,{value:g,children:b})};return f.displayName=a+"Provider",[f,function(e){let f=d.useContext(c);if(f)return f;if(void 0!==b)return b;throw Error(`\`${e}\` must be used within \`${a}\``)}]}function g(a,b=[]){let c=[],f=()=>{let b=c.map(a=>d.createContext(a));return function(c){let e=c?.[a]||b;return d.useMemo(()=>({[`__scope${a}`]:{...c,[a]:e}}),[c,e])}};return f.scopeName=a,[function(b,f){let g=d.createContext(f),h=c.length;c=[...c,f];let i=b=>{let{scope:c,children:f,...i}=b,j=c?.[a]?.[h]||g,k=d.useMemo(()=>i,Object.values(i));return(0,e.jsx)(j.Provider,{value:k,children:f})};return i.displayName=b+"Provider",[i,function(c,e){let i=e?.[a]?.[h]||g,j=d.useContext(i);if(j)return j;if(void 0!==f)return f;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return d.useMemo(()=>({[`__scope${b.scopeName}`]:e}),[e])}};return c.scopeName=b.scopeName,c}(f,...b)]}},12369:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["shares",{children:["[shareToken]",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,62302)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\[shareToken]\\analytics\\page.tsx"]}]},{}]},{}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\[shareToken]\\analytics\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/shares/[shareToken]/analytics/page",pathname:"/dashboard/shares/[shareToken]/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/shares/[shareToken]/analytics/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},12412:a=>{"use strict";a.exports=require("assert")},13495:(a,b,c)=>{"use strict";c.d(b,{c:()=>e});var d=c(43210);function e(a){let b=d.useRef(a);return d.useEffect(()=>{b.current=a}),d.useMemo(()=>(...a)=>b.current?.(...a),[])}},14163:(a,b,c)=>{"use strict";c.d(b,{hO:()=>i,sG:()=>h});var d=c(43210),e=c(51215),f=c(8730),g=c(60687),h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,f.TL)(`Primitive.${b}`),e=d.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return e.displayName=`Primitive.${b}`,{...a,[b]:e}},{});function i(a,b){a&&e.flushSync(()=>a.dispatchEvent(b))}},15079:(a,b,c)=>{"use strict";c.d(b,{bq:()=>j,eb:()=>m,gC:()=>l,l6:()=>i,yv:()=>k});var d=c(60687),e=c(43210),f=c(78272),g=c(4780);let h=e.createContext({open:!1,setOpen:()=>{}}),i=({value:a,onValueChange:b,children:c})=>{let[f,g]=e.useState(!1);return(0,d.jsx)(h.Provider,{value:{value:a,onValueChange:b,open:f,setOpen:g},children:(0,d.jsx)("div",{className:"relative",children:c})})},j=({className:a,children:b})=>{let{open:c,setOpen:i}=e.useContext(h);return(0,d.jsxs)("button",{type:"button",className:(0,g.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),onClick:()=>i(!c),children:[b,(0,d.jsx)(f.A,{className:"h-4 w-4 opacity-50"})]})},k=({placeholder:a,className:b})=>{let{value:c}=e.useContext(h);return(0,d.jsx)("span",{className:(0,g.cn)("block truncate",b),children:c||a})},l=({className:a,children:b})=>{let{open:c,setOpen:f}=e.useContext(h);return c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>f(!1)}),(0,d.jsx)("div",{className:(0,g.cn)("absolute z-50 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-white p-1 text-gray-950 shadow-md animate-in fade-in-0 zoom-in-95",a),children:b})]}):null},m=({value:a,className:b,children:c,onSelect:f})=>{let{onValueChange:i,setOpen:j}=e.useContext(h);return(0,d.jsx)("div",{className:(0,g.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-gray-100 focus:bg-gray-100 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",b),onClick:()=>{i?.(a),j(!1),f?.()},children:c})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28559:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},29867:(a,b,c)=>{"use strict";c.d(b,{d:()=>i});var d=c(43210);let e={toasts:[]},f=[];function g(){f.forEach(a=>a(e))}function h(a){let b=e.toasts.findIndex(b=>b.id===a);b>-1&&(e.toasts.splice(b,1),g())}function i(){let[a,b]=(0,d.useState)(e),c=(0,d.useCallback)(a=>(f.push(a),()=>{f=f.filter(b=>b!==a)}),[]),i=(0,d.useCallback)(a=>(function(a){let b=Math.random().toString(36).substr(2,9),c={id:b,duration:5e3,...a};return e.toasts.push(c),g(),c.duration&&c.duration>0&&setTimeout(()=>{h(b)},c.duration),b})(a),[]),j=(0,d.useCallback)(a=>{h(a)},[]);return(0,d.useState)(()=>c(b)),{toast:i,dismiss:j,toasts:a.toasts}}},33872:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43711:(a,b,c)=>{Promise.resolve().then(c.bind(c,89803))},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},46059:(a,b,c)=>{"use strict";c.d(b,{C:()=>g});var d=c(43210),e=c(98599),f=c(66156),g=a=>{let{present:b,children:c}=a,g=function(a){var b,c;let[e,g]=d.useState(),i=d.useRef(null),j=d.useRef(a),k=d.useRef("none"),[l,m]=(b=a?"mounted":"unmounted",c={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},d.useReducer((a,b)=>c[a][b]??a,b));return d.useEffect(()=>{let a=h(i.current);k.current="mounted"===l?a:"none"},[l]),(0,f.N)(()=>{let b=i.current,c=j.current;if(c!==a){let d=k.current,e=h(b);a?m("MOUNT"):"none"===e||b?.display==="none"?m("UNMOUNT"):c&&d!==e?m("ANIMATION_OUT"):m("UNMOUNT"),j.current=a}},[a,m]),(0,f.N)(()=>{if(e){let a,b=e.ownerDocument.defaultView??window,c=c=>{let d=h(i.current).includes(c.animationName);if(c.target===e&&d&&(m("ANIMATION_END"),!j.current)){let c=e.style.animationFillMode;e.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===e.style.animationFillMode&&(e.style.animationFillMode=c)})}},d=a=>{a.target===e&&(k.current=h(i.current))};return e.addEventListener("animationstart",d),e.addEventListener("animationcancel",c),e.addEventListener("animationend",c),()=>{b.clearTimeout(a),e.removeEventListener("animationstart",d),e.removeEventListener("animationcancel",c),e.removeEventListener("animationend",c)}}m("ANIMATION_END")},[e,m]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:d.useCallback(a=>{i.current=a?getComputedStyle(a):null,g(a)},[])}}(b),i="function"==typeof c?c({present:g.isPresent}):d.Children.only(c),j=(0,e.s)(g.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(i));return"function"==typeof c||g.isPresent?d.cloneElement(i,{ref:j}):null};function h(a){return a?.animationName||"none"}g.displayName="Presence"},53411:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},62302:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\Web\\\\pakarai\\\\ai-trainer\\\\fe\\\\src\\\\app\\\\dashboard\\\\shares\\\\[shareToken]\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\[shareToken]\\analytics\\page.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65551:(a,b,c)=>{"use strict";c.d(b,{i:()=>h});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useInsertionEffect ".trim().toString()]||f.N;function h({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[f,h,i]=function({defaultProp:a,onChange:b}){let[c,d]=e.useState(a),f=e.useRef(c),h=e.useRef(b);return g(()=>{h.current=b},[b]),e.useEffect(()=>{f.current!==c&&(h.current?.(c),f.current=c)},[c,f]),[c,d,h]}({defaultProp:b,onChange:c}),j=void 0!==a,k=j?a:f;{let b=e.useRef(void 0!==a);e.useEffect(()=>{let a=b.current;if(a!==j){let b=j?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=j},[j,d])}return[k,e.useCallback(b=>{if(j){let c="function"==typeof b?b(a):b;c!==a&&i.current?.(c)}else h(b)},[j,a,h,i])]}Symbol("RADIX:SYNC_STATE")},66156:(a,b,c)=>{"use strict";c.d(b,{N:()=>e});var d=c(43210),e=globalThis?.document?d.useLayoutEffect:()=>{}},70569:(a,b,c)=>{"use strict";function d(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}c.d(b,{m:()=>d})},74075:a=>{"use strict";a.exports=require("zlib")},78122:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78215:(a,b,c)=>{Promise.resolve().then(c.bind(c,62302))},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},85763:(a,b,c)=>{"use strict";c.d(b,{Xi:()=>i,av:()=>j,j7:()=>h,tU:()=>g});var d=c(60687);c(43210);var e=c(54304),f=c(4780);function g({className:a,...b}){return(0,d.jsx)(e.bL,{"data-slot":"tabs",className:(0,f.cn)("flex flex-col gap-2",a),...b})}function h({className:a,...b}){return(0,d.jsx)(e.B8,{"data-slot":"tabs-list",className:(0,f.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...b})}function i({className:a,...b}){return(0,d.jsx)(e.l9,{"data-slot":"tabs-trigger",className:(0,f.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...b})}function j({className:a,...b}){return(0,d.jsx)(e.UC,{"data-slot":"tabs-content",className:(0,f.cn)("flex-1 outline-none",a),...b})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89803:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>B});var d=c(60687),e=c(16189),f=c(43210),g=c(44493),h=c(29523),i=c(96834),j=c(85763),k=c(15079),l=c(62688);let m=(0,l.A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]),n=(0,l.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);var o=c(53411),p=c(28559),q=c(78122);let r=(0,l.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),s=(0,l.A)("mouse-pointer",[["path",{d:"M12.586 12.586 19 19",key:"ea5xo7"}],["path",{d:"M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z",key:"277e5u"}]]);var t=c(33872),u=c(41312),v=c(40228);let w=(0,l.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var x=c(29867),y=c(62185);function z({shareToken:a}){let{toast:b}=(0,x.d)(),c=(0,e.useRouter)(),[l,z]=(0,f.useState)(null),[A,B]=(0,f.useState)(!0),[C,D]=(0,f.useState)("7d"),[E,F]=(0,f.useState)(!1),G=(0,f.useCallback)(async()=>{try{B(!0);let b=await y.FH.get(`/sharing/analytics/${a}?timeRange=${C}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});b.data.success&&z(b.data.data)}catch(a){console.error("Error loading analytics:",a),b({title:"Error",description:"Failed to load analytics data. Please try again.",variant:"destructive"})}finally{B(!1)}},[a,C,b]),H=async()=>{F(!0),await G(),F(!1),b({title:"Analytics Refreshed",description:"Analytics data has been updated with the latest information."})},I=async()=>{try{let c=await y.FH.get(`/sharing/analytics/${a}/export?timeRange=${C}&format=csv`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}}),d=new Blob([c.data],{type:"text/csv"}),e=window.URL.createObjectURL(d),f=document.createElement("a");f.href=e,f.download=`share-analytics-${a}-${C}.csv`,document.body.appendChild(f),f.click(),document.body.removeChild(f),window.URL.revokeObjectURL(e),b({title:"Export Complete",description:"Analytics data has been exported successfully."})}catch(a){console.error("Error exporting data:",a),b({title:"Export Failed",description:"Failed to export analytics data. Please try again.",variant:"destructive"})}};if(A)return(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsx)(g.Zp,{children:(0,d.jsxs)(g.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-2"}),(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-3/4"})]})},b))})]})});if(!l)return(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(o.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Analytics Data"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Analytics data is not available for this share."}),(0,d.jsxs)(h.$,{onClick:()=>c.back(),className:"mt-4",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Go Back"]})]});let{share:J,analytics:K}=l;return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)(h.$,{variant:"ghost",onClick:()=>c.back(),children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:J.expert.name}),(0,d.jsx)("p",{className:"text-gray-600",children:"Share Analytics"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 mt-4 sm:mt-0",children:[(0,d.jsxs)(k.l6,{value:C,onValueChange:D,children:[(0,d.jsx)(k.bq,{className:"w-32",children:(0,d.jsx)(k.yv,{})}),(0,d.jsxs)(k.gC,{children:[(0,d.jsx)(k.eb,{value:"24h",children:"Last 24h"}),(0,d.jsx)(k.eb,{value:"7d",children:"Last 7 days"}),(0,d.jsx)(k.eb,{value:"30d",children:"Last 30 days"}),(0,d.jsx)(k.eb,{value:"90d",children:"Last 90 days"})]})]}),(0,d.jsxs)(h.$,{variant:"outline",onClick:H,disabled:E,children:[(0,d.jsx)(q.A,{className:`h-4 w-4 mr-2 ${E?"animate-spin":""}`}),"Refresh"]}),(0,d.jsxs)(h.$,{variant:"outline",onClick:I,children:[(0,d.jsx)(r,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,d.jsx)(g.Zp,{children:(0,d.jsx)(g.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900",children:J.expert.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:J.expert.description})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(i.E,{variant:J.isActive?"default":"secondary",children:J.isActive?"Active":"Inactive"}),(0,d.jsx)(i.E,{variant:"outline",className:"capitalize",children:J.shareType})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Created"}),(0,d.jsx)("p",{className:"font-medium",children:new Date(J.createdAt).toLocaleDateString()})]})]})})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,d.jsx)(g.Zp,{children:(0,d.jsxs)(g.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-blue-600 mb-2",children:[(0,d.jsx)(s,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"Total Clicks"})]}),(0,d.jsx)("p",{className:"text-3xl font-bold text-blue-900",children:K.totalClicks.toLocaleString()}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[K.uniqueVisitors," unique visitors"]})]})}),(0,d.jsx)(g.Zp,{children:(0,d.jsxs)(g.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-green-600 mb-2",children:[(0,d.jsx)(t.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"Conversions"})]}),(0,d.jsx)("p",{className:"text-3xl font-bold text-green-900",children:K.totalConversions.toLocaleString()}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[K.conversionRate.toFixed(1),"% conversion rate"]})]})}),(0,d.jsx)(g.Zp,{children:(0,d.jsxs)(g.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-purple-600 mb-2",children:[(0,d.jsx)(u.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"Returning Visitors"})]}),(0,d.jsx)("p",{className:"text-3xl font-bold text-purple-900",children:K.returningVisitors.toLocaleString()}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[(K.returningVisitors/K.uniqueVisitors*100).toFixed(1),"% return rate"]})]})}),(0,d.jsx)(g.Zp,{children:(0,d.jsxs)(g.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-orange-600 mb-2",children:[(0,d.jsx)(v.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"Avg. Session"})]}),(0,d.jsxs)("p",{className:"text-3xl font-bold text-orange-900",children:[Math.round(K.avgSessionDuration/60),"m"]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[K.avgSessionDuration,"s average"]})]})})]}),(0,d.jsxs)(j.tU,{defaultValue:"traffic",className:"space-y-6",children:[(0,d.jsxs)(j.j7,{children:[(0,d.jsx)(j.Xi,{value:"traffic",children:"Traffic Sources"}),(0,d.jsx)(j.Xi,{value:"devices",children:"Devices"}),(0,d.jsx)(j.Xi,{value:"geography",children:"Geography"}),(0,d.jsx)(j.Xi,{value:"timeline",children:"Timeline"})]}),(0,d.jsx)(j.av,{value:"traffic",className:"space-y-6",children:(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsx)(g.ZB,{children:"Top Referrers"}),(0,d.jsx)(g.BT,{children:"Sources that drive the most traffic to your shared expert"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:K.topReferrers.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(w,{className:"h-5 w-5 text-gray-400"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium",children:a.source||"Direct"}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[a.percentage.toFixed(1),"% of traffic"]})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("p",{className:"font-bold",children:a.clicks}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"clicks"})]})]},b))})})]})}),(0,d.jsx)(j.av,{value:"devices",className:"space-y-6",children:(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsx)(g.ZB,{children:"Device Breakdown"}),(0,d.jsx)(g.BT,{children:"How users access your shared expert across different devices"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:Object.entries(K.deviceBreakdown).map(([a,b])=>{let c=b/K.totalClicks*100;return(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(a=>{switch(a){case"desktop":default:return(0,d.jsx)(m,{className:"h-4 w-4"});case"mobile":case"tablet":return(0,d.jsx)(n,{className:"h-4 w-4"})}})(a),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium capitalize",children:a}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[c.toFixed(1),"% of traffic"]})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("p",{className:"font-bold",children:b}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"clicks"})]})]},a)})})})]})}),(0,d.jsx)(j.av,{value:"geography",className:"space-y-6",children:(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsx)(g.ZB,{children:"Geographic Distribution"}),(0,d.jsx)(g.BT,{children:"Where your shared expert is being accessed from around the world"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:K.geographicData.slice(0,10).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(w,{className:"h-5 w-5 text-gray-400"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium",children:a.country}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[(a.clicks/K.totalClicks*100).toFixed(1),"% of traffic"]})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("p",{className:"font-bold",children:[a.clicks," clicks"]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[a.conversions," conversions"]})]})]},b))})})]})}),(0,d.jsx)(j.av,{value:"timeline",className:"space-y-6",children:(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsx)(g.ZB,{children:"Performance Over Time"}),(0,d.jsx)(g.BT,{children:"Track how your shared expert performs across different time periods"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium mb-4",children:"Daily Performance"}),(0,d.jsx)("div",{className:"space-y-2",children:K.timeSeriesData.slice(-7).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded",children:[(0,d.jsx)("div",{children:(0,d.jsx)("p",{className:"font-medium",children:new Date(a.date).toLocaleDateString()})}),(0,d.jsxs)("div",{className:"flex space-x-6 text-sm",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"font-bold text-blue-600",children:a.clicks}),(0,d.jsx)("p",{className:"text-gray-600",children:"clicks"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"font-bold text-green-600",children:a.conversions}),(0,d.jsx)("p",{className:"text-gray-600",children:"conversions"})]})]})]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium mb-4",children:"Peak Hours"}),(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:K.hourlyData.sort((a,b)=>b.clicks-a.clicks).slice(0,8).map((a,b)=>(0,d.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,d.jsxs)("p",{className:"font-bold",children:[a.hour,":00"]}),(0,d.jsxs)("p",{className:"text-sm text-blue-600",children:[a.clicks," clicks"]}),(0,d.jsxs)("p",{className:"text-sm text-green-600",children:[a.conversions," chats"]})]},b))})]})]})})]})})]})]})}function A(){let a=(0,e.useParams)().shareToken;return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("div",{className:"bg-white border-b",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"py-6",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Share Analytics"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"Detailed performance insights for your shared expert"})]})})}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsx)(z,{shareToken:a})})]})}function B(){return(0,d.jsx)(A,{})}},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(4780);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},96963:(a,b,c)=>{"use strict";c.d(b,{B:()=>i});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useId ".trim().toString()]||(()=>void 0),h=0;function i(a){let[b,c]=e.useState(g());return(0,f.N)(()=>{a||c(a=>a??String(h++))},[a]),a||(b?`radix-${b}`:"")}}};var b=require("../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,842,928,304,263],()=>b(b.s=12369));module.exports=c})();