"use strict";exports.id=235,exports.ids=[235],exports.modules={11273:(a,b,c)=>{c.d(b,{A:()=>g,q:()=>f});var d=c(43210),e=c(60687);function f(a,b){let c=d.createContext(b),f=a=>{let{children:b,...f}=a,g=d.useMemo(()=>f,Object.values(f));return(0,e.jsx)(c.Provider,{value:g,children:b})};return f.displayName=a+"Provider",[f,function(e){let f=d.useContext(c);if(f)return f;if(void 0!==b)return b;throw Error(`\`${e}\` must be used within \`${a}\``)}]}function g(a,b=[]){let c=[],f=()=>{let b=c.map(a=>d.createContext(a));return function(c){let e=c?.[a]||b;return d.useMemo(()=>({[`__scope${a}`]:{...c,[a]:e}}),[c,e])}};return f.scopeName=a,[function(b,f){let g=d.createContext(f),h=c.length;c=[...c,f];let i=b=>{let{scope:c,children:f,...i}=b,j=c?.[a]?.[h]||g,k=d.useMemo(()=>i,Object.values(i));return(0,e.jsx)(j.Provider,{value:k,children:f})};return i.displayName=b+"Provider",[i,function(c,e){let i=e?.[a]?.[h]||g,j=d.useContext(i);if(j)return j;if(void 0!==f)return f;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return d.useMemo(()=>({[`__scope${b.scopeName}`]:e}),[e])}};return c.scopeName=b.scopeName,c}(f,...b)]}},11860:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13495:(a,b,c)=>{c.d(b,{c:()=>e});var d=c(43210);function e(a){let b=d.useRef(a);return d.useEffect(()=>{b.current=a}),d.useMemo(()=>(...a)=>b.current?.(...a),[])}},14163:(a,b,c)=>{c.d(b,{hO:()=>i,sG:()=>h});var d=c(43210),e=c(51215),f=c(8730),g=c(60687),h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,f.TL)(`Primitive.${b}`),e=d.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return e.displayName=`Primitive.${b}`,{...a,[b]:e}},{});function i(a,b){a&&e.flushSync(()=>a.dispatchEvent(b))}},46059:(a,b,c)=>{c.d(b,{C:()=>g});var d=c(43210),e=c(98599),f=c(66156),g=a=>{let{present:b,children:c}=a,g=function(a){var b,c;let[e,g]=d.useState(),i=d.useRef(null),j=d.useRef(a),k=d.useRef("none"),[l,m]=(b=a?"mounted":"unmounted",c={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},d.useReducer((a,b)=>c[a][b]??a,b));return d.useEffect(()=>{let a=h(i.current);k.current="mounted"===l?a:"none"},[l]),(0,f.N)(()=>{let b=i.current,c=j.current;if(c!==a){let d=k.current,e=h(b);a?m("MOUNT"):"none"===e||b?.display==="none"?m("UNMOUNT"):c&&d!==e?m("ANIMATION_OUT"):m("UNMOUNT"),j.current=a}},[a,m]),(0,f.N)(()=>{if(e){let a,b=e.ownerDocument.defaultView??window,c=c=>{let d=h(i.current).includes(c.animationName);if(c.target===e&&d&&(m("ANIMATION_END"),!j.current)){let c=e.style.animationFillMode;e.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===e.style.animationFillMode&&(e.style.animationFillMode=c)})}},d=a=>{a.target===e&&(k.current=h(i.current))};return e.addEventListener("animationstart",d),e.addEventListener("animationcancel",c),e.addEventListener("animationend",c),()=>{b.clearTimeout(a),e.removeEventListener("animationstart",d),e.removeEventListener("animationcancel",c),e.removeEventListener("animationend",c)}}m("ANIMATION_END")},[e,m]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:d.useCallback(a=>{i.current=a?getComputedStyle(a):null,g(a)},[])}}(b),i="function"==typeof c?c({present:g.isPresent}):d.Children.only(c),j=(0,e.s)(g.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(i));return"function"==typeof c||g.isPresent?d.cloneElement(i,{ref:j}):null};function h(a){return a?.animationName||"none"}g.displayName="Presence"},65551:(a,b,c)=>{c.d(b,{i:()=>h});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useInsertionEffect ".trim().toString()]||f.N;function h({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[f,h,i]=function({defaultProp:a,onChange:b}){let[c,d]=e.useState(a),f=e.useRef(c),h=e.useRef(b);return g(()=>{h.current=b},[b]),e.useEffect(()=>{f.current!==c&&(h.current?.(c),f.current=c)},[c,f]),[c,d,h]}({defaultProp:b,onChange:c}),j=void 0!==a,k=j?a:f;{let b=e.useRef(void 0!==a);e.useEffect(()=>{let a=b.current;if(a!==j){let b=j?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=j},[j,d])}return[k,e.useCallback(b=>{if(j){let c="function"==typeof b?b(a):b;c!==a&&i.current?.(c)}else h(b)},[j,a,h,i])]}Symbol("RADIX:SYNC_STATE")},66156:(a,b,c)=>{c.d(b,{N:()=>e});var d=c(43210),e=globalThis?.document?d.useLayoutEffect:()=>{}},70569:(a,b,c)=>{c.d(b,{m:()=>d});function d(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}},80882:(a,b,c)=>{c.d(b,{bm:()=>bf,UC:()=>bc,VY:()=>be,hJ:()=>bb,ZL:()=>ba,bL:()=>a8,hE:()=>bd,l9:()=>a9});var d,e,f,g=c(43210),h=c(70569),i=c(98599),j=c(11273),k=c(96963),l=c(65551),m=c(14163),n=c(13495),o=c(60687),p="dismissableLayer.update",q=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),r=g.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:j,onInteractOutside:k,onDismiss:l,...r}=a,u=g.useContext(q),[v,w]=g.useState(null),x=v?.ownerDocument??globalThis?.document,[,y]=g.useState({}),z=(0,i.s)(b,a=>w(a)),A=Array.from(u.layers),[B]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(B),D=v?A.indexOf(v):-1,E=u.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let c=(0,n.c)(a),d=g.useRef(!1),e=g.useRef(()=>{});return g.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){t("dismissableLayer.pointerDownOutside",c,f,{discrete:!0})},f={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",e.current),e.current=d,b.addEventListener("click",e.current,{once:!0})):d()}else b.removeEventListener("click",e.current);d.current=!1},f=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(f),b.removeEventListener("pointerdown",a),b.removeEventListener("click",e.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...u.branches].some(a=>a.contains(b));F&&!c&&(f?.(a),k?.(a),a.defaultPrevented||l?.())},x),H=function(a,b=globalThis?.document){let c=(0,n.c)(a),d=g.useRef(!1);return g.useEffect(()=>{let a=a=>{a.target&&!d.current&&t("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...u.branches].some(a=>a.contains(b))&&(j?.(a),k?.(a),a.defaultPrevented||l?.())},x);return!function(a,b=globalThis?.document){let c=(0,n.c)(a);g.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{D===u.layers.size-1&&(d?.(a),!a.defaultPrevented&&l&&(a.preventDefault(),l()))},x),g.useEffect(()=>{if(v)return c&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(e=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(v)),u.layers.add(v),s(),()=>{c&&1===u.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=e)}},[v,x,c,u]),g.useEffect(()=>()=>{v&&(u.layers.delete(v),u.layersWithOutsidePointerEventsDisabled.delete(v),s())},[v,u]),g.useEffect(()=>{let a=()=>y({});return document.addEventListener(p,a),()=>document.removeEventListener(p,a)},[]),(0,o.jsx)(m.sG.div,{...r,ref:z,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:(0,h.m)(a.onFocusCapture,H.onFocusCapture),onBlurCapture:(0,h.m)(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:(0,h.m)(a.onPointerDownCapture,G.onPointerDownCapture)})});function s(){let a=new CustomEvent(p);document.dispatchEvent(a)}function t(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,m.hO)(e,f):e.dispatchEvent(f)}r.displayName="DismissableLayer",g.forwardRef((a,b)=>{let c=g.useContext(q),d=g.useRef(null),e=(0,i.s)(b,d);return g.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,o.jsx)(m.sG.div,{...a,ref:e})}).displayName="DismissableLayerBranch";var u="focusScope.autoFocusOnMount",v="focusScope.autoFocusOnUnmount",w={bubbles:!1,cancelable:!0},x=g.forwardRef((a,b)=>{let{loop:c=!1,trapped:d=!1,onMountAutoFocus:e,onUnmountAutoFocus:f,...h}=a,[j,k]=g.useState(null),l=(0,n.c)(e),p=(0,n.c)(f),q=g.useRef(null),r=(0,i.s)(b,a=>k(a)),s=g.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;g.useEffect(()=>{if(d){let a=function(a){if(s.paused||!j)return;let b=a.target;j.contains(b)?q.current=b:A(q.current,{select:!0})},b=function(a){if(s.paused||!j)return;let b=a.relatedTarget;null!==b&&(j.contains(b)||A(q.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&A(j)});return j&&c.observe(j,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[d,j,s.paused]),g.useEffect(()=>{if(j){B.add(s);let a=document.activeElement;if(!j.contains(a)){let b=new CustomEvent(u,w);j.addEventListener(u,l),j.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(A(d,{select:b}),document.activeElement!==c)return}(y(j).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&A(j))}return()=>{j.removeEventListener(u,l),setTimeout(()=>{let b=new CustomEvent(v,w);j.addEventListener(v,p),j.dispatchEvent(b),b.defaultPrevented||A(a??document.body,{select:!0}),j.removeEventListener(v,p),B.remove(s)},0)}}},[j,l,p,s]);let t=g.useCallback(a=>{if(!c&&!d||s.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,e=document.activeElement;if(b&&e){let b=a.currentTarget,[d,f]=function(a){let b=y(a);return[z(b,a),z(b.reverse(),a)]}(b);d&&f?a.shiftKey||e!==f?a.shiftKey&&e===d&&(a.preventDefault(),c&&A(f,{select:!0})):(a.preventDefault(),c&&A(d,{select:!0})):e===b&&a.preventDefault()}},[c,d,s.paused]);return(0,o.jsx)(m.sG.div,{tabIndex:-1,...h,ref:r,onKeyDown:t})});function y(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function z(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function A(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}x.displayName="FocusScope";var B=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=C(a,b)).unshift(b)},remove(b){a=C(a,b),a[0]?.resume()}}}();function C(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}var D=c(51215),E=c(66156),F=g.forwardRef((a,b)=>{let{container:c,...d}=a,[e,f]=g.useState(!1);(0,E.N)(()=>f(!0),[]);let h=c||e&&globalThis?.document?.body;return h?D.createPortal((0,o.jsx)(m.sG.div,{...d,ref:b}),h):null});F.displayName="Portal";var G=c(46059),H=0;function I(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var J=function(){return(J=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function K(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var L=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),M="width-before-scroll-bar";function N(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var O="undefined"!=typeof window?g.useLayoutEffect:g.useEffect,P=new WeakMap;function Q(a){return a}var R=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=Q),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=J({async:!0,ssr:!1},a),e}(),S=function(){},T=g.forwardRef(function(a,b){var c,d,e,f,h=g.useRef(null),i=g.useState({onScrollCapture:S,onWheelCapture:S,onTouchMoveCapture:S}),j=i[0],k=i[1],l=a.forwardProps,m=a.children,n=a.className,o=a.removeScrollBar,p=a.enabled,q=a.shards,r=a.sideCar,s=a.noRelative,t=a.noIsolation,u=a.inert,v=a.allowPinchZoom,w=a.as,x=a.gapMode,y=K(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=(c=[h,b],d=function(a){return c.forEach(function(b){return N(b,a)})},(e=(0,g.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,f=e.facade,O(function(){var a=P.get(f);if(a){var b=new Set(a),d=new Set(c),e=f.current;b.forEach(function(a){d.has(a)||N(a,null)}),d.forEach(function(a){b.has(a)||N(a,e)})}P.set(f,c)},[c]),f),A=J(J({},y),j);return g.createElement(g.Fragment,null,p&&g.createElement(r,{sideCar:R,removeScrollBar:o,shards:q,noRelative:s,noIsolation:t,inert:u,setCallbacks:k,allowPinchZoom:!!v,lockRef:h,gapMode:x}),l?g.cloneElement(g.Children.only(m),J(J({},A),{ref:z})):g.createElement(void 0===w?"div":w,J({},A,{className:n,ref:z}),m))});T.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},T.classNames={fullWidth:M,zeroRight:L};var U=function(a){var b=a.sideCar,c=K(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return g.createElement(d,J({},c))};U.isSideCarExport=!0;var V=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=f||c.nc;return b&&a.setAttribute("nonce",b),a}())){var e,g;(e=b).styleSheet?e.styleSheet.cssText=d:e.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},W=function(){var a=V();return function(b,c){g.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},X=function(){var a=W();return function(b){return a(b.styles,b.dynamic),null}},Y={left:0,top:0,right:0,gap:0},Z=function(a){return parseInt(a||"",10)||0},$=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[Z(c),Z(d),Z(e)]},_=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return Y;var b=$(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},aa=X(),ab="data-scroll-locked",ac=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(ab,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(L," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(M," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(L," .").concat(L," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(M," .").concat(M," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(ab,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},ad=function(){var a=parseInt(document.body.getAttribute(ab)||"0",10);return isFinite(a)?a:0},ae=function(){g.useEffect(function(){return document.body.setAttribute(ab,(ad()+1).toString()),function(){var a=ad()-1;a<=0?document.body.removeAttribute(ab):document.body.setAttribute(ab,a.toString())}},[])},af=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;ae();var f=g.useMemo(function(){return _(e)},[e]);return g.createElement(aa,{styles:ac(f,!b,e,c?"":"!important")})},ag=!1;if("undefined"!=typeof window)try{var ah=Object.defineProperty({},"passive",{get:function(){return ag=!0,!0}});window.addEventListener("test",ah,ah),window.removeEventListener("test",ah,ah)}catch(a){ag=!1}var ai=!!ag&&{passive:!1},aj=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},ak=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),al(a,d)){var e=am(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},al=function(a,b){return"v"===a?aj(b,"overflowY"):aj(b,"overflowX")},am=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},an=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=am(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&al(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},ao=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},ap=function(a){return[a.deltaX,a.deltaY]},aq=function(a){return a&&"current"in a?a.current:a},ar=0,as=[];let at=(d=function(a){var b=g.useRef([]),c=g.useRef([0,0]),d=g.useRef(),e=g.useState(ar++)[0],f=g.useState(X)[0],h=g.useRef(a);g.useEffect(function(){h.current=a},[a]),g.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(aq),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=g.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!h.current.allowPinchZoom;var e,f=ao(a),g=c.current,i="deltaX"in a?a.deltaX:g[0]-f[0],j="deltaY"in a?a.deltaY:g[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=ak(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=ak(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return an(n,b,a,"h"===n?i:j,!0)},[]),j=g.useCallback(function(a){if(as.length&&as[as.length-1]===f){var c="deltaY"in a?ap(a):ao(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(h.current.shards||[]).map(aq).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!h.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=g.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=g.useCallback(function(a){c.current=ao(a),d.current=void 0},[]),m=g.useCallback(function(b){k(b.type,ap(b),b.target,i(b,a.lockRef.current))},[]),n=g.useCallback(function(b){k(b.type,ao(b),b.target,i(b,a.lockRef.current))},[]);g.useEffect(function(){return as.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,ai),document.addEventListener("touchmove",j,ai),document.addEventListener("touchstart",l,ai),function(){as=as.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,ai),document.removeEventListener("touchmove",j,ai),document.removeEventListener("touchstart",l,ai)}},[]);var o=a.removeScrollBar,p=a.inert;return g.createElement(g.Fragment,null,p?g.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?g.createElement(af,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},R.useMedium(d),U);var au=g.forwardRef(function(a,b){return g.createElement(T,J({},a,{ref:b,sideCar:at}))});au.classNames=T.classNames;var av=new WeakMap,aw=new WeakMap,ax={},ay=0,az=function(a){return a&&(a.host||az(a.parentNode))},aA=function(a,b,c,d){var e=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=az(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});ax[c]||(ax[c]=new WeakMap);var f=ax[c],g=[],h=new Set,i=new Set(e),j=function(a){!a||h.has(a)||(h.add(a),j(a.parentNode))};e.forEach(j);var k=function(a){!a||i.has(a)||Array.prototype.forEach.call(a.children,function(a){if(h.has(a))k(a);else try{var b=a.getAttribute(d),e=null!==b&&"false"!==b,i=(av.get(a)||0)+1,j=(f.get(a)||0)+1;av.set(a,i),f.set(a,j),g.push(a),1===i&&e&&aw.set(a,!0),1===j&&a.setAttribute(c,"true"),e||a.setAttribute(d,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return k(b),h.clear(),ay++,function(){g.forEach(function(a){var b=av.get(a)-1,e=f.get(a)-1;av.set(a,b),f.set(a,e),b||(aw.has(a)||a.removeAttribute(d),aw.delete(a)),e||a.removeAttribute(c)}),--ay||(av=new WeakMap,av=new WeakMap,aw=new WeakMap,ax={})}},aB=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),aA(d,e,c,"aria-hidden")):function(){return null}},aC=c(8730),aD="Dialog",[aE,aF]=(0,j.A)(aD),[aG,aH]=aE(aD),aI=a=>{let{__scopeDialog:b,children:c,open:d,defaultOpen:e,onOpenChange:f,modal:h=!0}=a,i=g.useRef(null),j=g.useRef(null),[m,n]=(0,l.i)({prop:d,defaultProp:e??!1,onChange:f,caller:aD});return(0,o.jsx)(aG,{scope:b,triggerRef:i,contentRef:j,contentId:(0,k.B)(),titleId:(0,k.B)(),descriptionId:(0,k.B)(),open:m,onOpenChange:n,onOpenToggle:g.useCallback(()=>n(a=>!a),[n]),modal:h,children:c})};aI.displayName=aD;var aJ="DialogTrigger",aK=g.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aH(aJ,c),f=(0,i.s)(b,e.triggerRef);return(0,o.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":e.open,"aria-controls":e.contentId,"data-state":a2(e.open),...d,ref:f,onClick:(0,h.m)(a.onClick,e.onOpenToggle)})});aK.displayName=aJ;var aL="DialogPortal",[aM,aN]=aE(aL,{forceMount:void 0}),aO=a=>{let{__scopeDialog:b,forceMount:c,children:d,container:e}=a,f=aH(aL,b);return(0,o.jsx)(aM,{scope:b,forceMount:c,children:g.Children.map(d,a=>(0,o.jsx)(G.C,{present:c||f.open,children:(0,o.jsx)(F,{asChild:!0,container:e,children:a})}))})};aO.displayName=aL;var aP="DialogOverlay",aQ=g.forwardRef((a,b)=>{let c=aN(aP,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=aH(aP,a.__scopeDialog);return f.modal?(0,o.jsx)(G.C,{present:d||f.open,children:(0,o.jsx)(aS,{...e,ref:b})}):null});aQ.displayName=aP;var aR=(0,aC.TL)("DialogOverlay.RemoveScroll"),aS=g.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aH(aP,c);return(0,o.jsx)(au,{as:aR,allowPinchZoom:!0,shards:[e.contentRef],children:(0,o.jsx)(m.sG.div,{"data-state":a2(e.open),...d,ref:b,style:{pointerEvents:"auto",...d.style}})})}),aT="DialogContent",aU=g.forwardRef((a,b)=>{let c=aN(aT,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=aH(aT,a.__scopeDialog);return(0,o.jsx)(G.C,{present:d||f.open,children:f.modal?(0,o.jsx)(aV,{...e,ref:b}):(0,o.jsx)(aW,{...e,ref:b})})});aU.displayName=aT;var aV=g.forwardRef((a,b)=>{let c=aH(aT,a.__scopeDialog),d=g.useRef(null),e=(0,i.s)(b,c.contentRef,d);return g.useEffect(()=>{let a=d.current;if(a)return aB(a)},[]),(0,o.jsx)(aX,{...a,ref:e,trapFocus:c.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,h.m)(a.onCloseAutoFocus,a=>{a.preventDefault(),c.triggerRef.current?.focus()}),onPointerDownOutside:(0,h.m)(a.onPointerDownOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey;(2===b.button||c)&&a.preventDefault()}),onFocusOutside:(0,h.m)(a.onFocusOutside,a=>a.preventDefault())})}),aW=g.forwardRef((a,b)=>{let c=aH(aT,a.__scopeDialog),d=g.useRef(!1),e=g.useRef(!1);return(0,o.jsx)(aX,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:b=>{a.onCloseAutoFocus?.(b),b.defaultPrevented||(d.current||c.triggerRef.current?.focus(),b.preventDefault()),d.current=!1,e.current=!1},onInteractOutside:b=>{a.onInteractOutside?.(b),b.defaultPrevented||(d.current=!0,"pointerdown"===b.detail.originalEvent.type&&(e.current=!0));let f=b.target;c.triggerRef.current?.contains(f)&&b.preventDefault(),"focusin"===b.detail.originalEvent.type&&e.current&&b.preventDefault()}})}),aX=g.forwardRef((a,b)=>{let{__scopeDialog:c,trapFocus:d,onOpenAutoFocus:e,onCloseAutoFocus:f,...h}=a,j=aH(aT,c),k=g.useRef(null),l=(0,i.s)(b,k);return g.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??I()),document.body.insertAdjacentElement("beforeend",a[1]??I()),H++,()=>{1===H&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),H--}},[]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(x,{asChild:!0,loop:!0,trapped:d,onMountAutoFocus:e,onUnmountAutoFocus:f,children:(0,o.jsx)(r,{role:"dialog",id:j.contentId,"aria-describedby":j.descriptionId,"aria-labelledby":j.titleId,"data-state":a2(j.open),...h,ref:l,onDismiss:()=>j.onOpenChange(!1)})}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(a6,{titleId:j.titleId}),(0,o.jsx)(a7,{contentRef:k,descriptionId:j.descriptionId})]})]})}),aY="DialogTitle",aZ=g.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aH(aY,c);return(0,o.jsx)(m.sG.h2,{id:e.titleId,...d,ref:b})});aZ.displayName=aY;var a$="DialogDescription",a_=g.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aH(a$,c);return(0,o.jsx)(m.sG.p,{id:e.descriptionId,...d,ref:b})});a_.displayName=a$;var a0="DialogClose",a1=g.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=aH(a0,c);return(0,o.jsx)(m.sG.button,{type:"button",...d,ref:b,onClick:(0,h.m)(a.onClick,()=>e.onOpenChange(!1))})});function a2(a){return a?"open":"closed"}a1.displayName=a0;var a3="DialogTitleWarning",[a4,a5]=(0,j.q)(a3,{contentName:aT,titleName:aY,docsSlug:"dialog"}),a6=({titleId:a})=>{let b=a5(a3),c=`\`${b.contentName}\` requires a \`${b.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${b.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${b.docsSlug}`;return g.useEffect(()=>{a&&(document.getElementById(a)||console.error(c))},[c,a]),null},a7=({contentRef:a,descriptionId:b})=>{let c=a5("DialogDescriptionWarning"),d=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${c.contentName}}.`;return g.useEffect(()=>{let c=a.current?.getAttribute("aria-describedby");b&&c&&(document.getElementById(b)||console.warn(d))},[d,a,b]),null},a8=aI,a9=aK,ba=aO,bb=aQ,bc=aU,bd=aZ,be=a_,bf=a1},96963:(a,b,c)=>{c.d(b,{B:()=>i});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useId ".trim().toString()]||(()=>void 0),h=0;function i(a){let[b,c]=e.useState(g());return(0,f.N)(()=>{a||c(a=>a??String(h++))},[a]),a||(b?`radix-${b}`:"")}}};