const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

// Import middleware
const corsMiddleware = require('./middleware/cors');
const { requestLogger, errorLogger } = require('./middleware/logger');
const errorHandler = require('./middleware/errorHandler');

// Import routes
const userRoutes = require('./routes/userRoutes');
const expertRoutes = require('./routes/expertRoutes');
const filterRoutes = require('./routes/experts/filterRoutes');
const chatRoutes = require('./routes/chat');
const assistantRoutes = require('./routes/assistants');
const balanceRoutes = require('./routes/balance');
const affiliateRoutes = require('./routes/affiliate');
const reviewRoutes = require('./routes/reviews');
const aiGenerationRoutes = require('./routes/aiGeneration');
const socketStatsRoutes = require('./routes/socket-stats');
const sharingRoutes = require('./routes/sharing');
const recommendationRoutes = require('./routes/recommendations');

// Import Swagger configuration
const { swaggerUi, swaggerSpec } = require('./config/swagger');

// Create Express app
const app = express();

// Trust proxy for proper IP detection
app.set('trust proxy', 1);

// Apply CORS middleware
app.use(corsMiddleware);

// Body parsing middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Request logging middleware
app.use(requestLogger);

// Static files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Setup Swagger documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API Routes
app.use('/api/users', userRoutes);
app.use('/api/experts', expertRoutes);
app.use('/api/experts', filterRoutes); // Add filter routes under /api/experts
app.use('/api/chat', chatRoutes);
app.use('/api/assistants', assistantRoutes);
app.use('/api/balance', balanceRoutes);
app.use('/api/affiliate', affiliateRoutes);
app.use('/api/reviews', reviewRoutes);
app.use('/api/ai-generation', aiGenerationRoutes);
app.use('/api/socket-stats', socketStatsRoutes);
app.use('/api/sharing', sharingRoutes);
app.use('/api/recommendations', recommendationRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl
  });
});

// Global error handler (must be last)
app.use(errorHandler);

module.exports = app;