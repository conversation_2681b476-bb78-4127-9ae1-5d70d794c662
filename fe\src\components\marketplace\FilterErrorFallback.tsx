"use client";

import React from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { AlertTriangle, RefreshCw, Filter, TrendingUp } from "lucide-react";
import { useFilters } from "@/hooks/useFilters";

interface FilterErrorFallbackProps {
  error?: Error;
  onRetry?: () => void;
  showTrendingFallback?: boolean;
}

export const FilterErrorFallback: React.FC<FilterErrorFallbackProps> = ({
  error,
  onRetry,
  showTrendingFallback = true
}) => {
  const { actions } = useFilters();

  const handleFallbackToTrending = () => {
    actions.setFilter('trending');
    actions.setTimeline('last-30-days');
    actions.setSearchQuery('');
  };

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className="space-y-4">
      {/* Error Alert */}
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Filter Error</AlertTitle>
        <AlertDescription>
          {error?.message || "We're having trouble loading the filtered results. This might be due to a temporary server issue."}
        </AlertDescription>
      </Alert>

      {/* Action Cards */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Retry Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-3">
              <RefreshCw className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold">Try Again</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Retry the current filter selection. This often resolves temporary connection issues.
            </p>
            <Button onClick={handleRetry} className="w-full">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Current Filter
            </Button>
          </CardContent>
        </Card>

        {/* Trending Fallback Card */}
        {showTrendingFallback && (
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2 mb-3">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <h3 className="font-semibold">View Trending</h3>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Switch to trending experts while we resolve the issue with your current filter.
              </p>
              <Button
                onClick={handleFallbackToTrending}
                variant="outline"
                className="w-full"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Show Trending Experts
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Additional Help */}
      <Card className="bg-gray-50">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <Filter className="h-5 w-5 text-gray-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-sm mb-1">Having persistent issues?</h4>
              <p className="text-xs text-gray-600 mb-2">
                Try clearing your browser cache or switching to a different filter option.
                If the problem continues, please contact our support team.
              </p>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => localStorage.clear()}
                  className="text-xs h-7"
                >
                  Clear Cache
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => window.location.href = '/support'}
                  className="text-xs h-7"
                >
                  Contact Support
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};