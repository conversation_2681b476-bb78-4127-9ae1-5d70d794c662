(()=>{var a={};a.id=946,a.ids=[946],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6142:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>U});var d=c(60687),e=c(43210),f=c(29523),g=c(44493),h=c(85763),i=c(96474),j=c(81620),k=c(13861),l=c(33872),m=c(25541),n=c(53411),o=c(29867),p=c(72085),q=c(3315),r=c(96834),s=c(89667),t=c(78272),u=c(4780);let v=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsx)("div",{ref:e,className:(0,u.cn)("relative inline-block text-left",a),...c,children:b}));v.displayName="DropdownMenu";let w=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)("button",{ref:e,className:(0,u.cn)("inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",a),...c,children:[b,(0,d.jsx)(t.A,{className:"-mr-1 ml-2 h-5 w-5","aria-hidden":"true"})]}));w.displayName="DropdownMenuTrigger";let x=e.forwardRef(({className:a,align:b="center",...c},e)=>(0,d.jsx)("div",{ref:e,className:(0,u.cn)("absolute z-50 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none","end"===b&&"right-0","start"===b&&"left-0","center"===b&&"left-1/2 transform -translate-x-1/2",a),...c}));x.displayName="DropdownMenuContent";let y=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,u.cn)("block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 cursor-pointer",a),...b}));y.displayName="DropdownMenuItem",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,u.cn)("border-t border-gray-100 my-1",a),...b})).displayName="DropdownMenuSeparator",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,u.cn)("px-4 py-2 text-sm font-medium text-gray-900",a),...b})).displayName="DropdownMenuLabel";var z=c(11860);let A=e.createContext(void 0),B=()=>{let a=e.useContext(A);if(!a)throw Error("useAlertDialog must be used within AlertDialog");return a},C=e.forwardRef(({className:a,open:b=!1,onOpenChange:c,children:f,...g},h)=>{let[i,j]=e.useState(b),k=void 0!==c,l=k?b:i,m=k?c:j;return(0,d.jsx)(A.Provider,{value:{open:l,setOpen:m},children:(0,d.jsxs)("div",{ref:h,className:a,...g,children:[f,l&&(0,d.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,d.jsx)("div",{className:"fixed inset-0 bg-black/50",onClick:()=>m(!1)}),(0,d.jsx)("div",{className:"relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4",children:f})]})]})})});C.displayName="AlertDialog",e.forwardRef(({className:a,onClick:b,...c},e)=>{let{setOpen:f}=B();return(0,d.jsx)("button",{ref:e,className:a,onClick:a=>{f(!0),b?.(a)},...c})}).displayName="AlertDialogTrigger";let D=e.forwardRef(({className:a,children:b,...c},e)=>{let{open:f,setOpen:g}=B();return f?(0,d.jsxs)("div",{ref:e,className:(0,u.cn)("p-6",a),...c,children:[(0,d.jsx)("button",{className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600",onClick:()=>g(!1),children:(0,d.jsx)(z.A,{className:"h-4 w-4"})}),b]}):null});D.displayName="AlertDialogContent";let E=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,u.cn)("mb-4",a),...b}));E.displayName="AlertDialogHeader";let F=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h2",{ref:c,className:(0,u.cn)("text-lg font-semibold text-gray-900",a),...b}));F.displayName="AlertDialogTitle";let G=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,u.cn)("text-sm text-gray-600 mt-2",a),...b}));G.displayName="AlertDialogDescription";let H=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,u.cn)("flex justify-end space-x-2 mt-6",a),...b}));H.displayName="AlertDialogFooter";let I=e.forwardRef(({className:a,onClick:b,...c},e)=>{let{setOpen:f}=B();return(0,d.jsx)("button",{ref:e,className:(0,u.cn)("px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500",a),onClick:a=>{b?.(a),f(!1)},...c})});I.displayName="AlertDialogAction";let J=e.forwardRef(({className:a,onClick:b,...c},e)=>{let{setOpen:f}=B();return(0,d.jsx)("button",{ref:e,className:(0,u.cn)("px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500",a),onClick:a=>{b?.(a),f(!1)},...c})});J.displayName="AlertDialogCancel";var K=c(99270),L=c(80462),M=c(40228),N=c(13964),O=c(70615);let P=(0,c(62688).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var Q=c(25334),R=c(88233),S=c(62185);function T({refreshTrigger:a}){let{toast:b}=(0,o.d)(),[c,h]=(0,e.useState)([]),[i,m]=(0,e.useState)(!0),[p,q]=(0,e.useState)(""),[t,u]=(0,e.useState)("all"),[z,A]=(0,e.useState)(!1),[B,T]=(0,e.useState)(null),[U,V]=(0,e.useState)(null);(0,e.useCallback)(async()=>{try{m(!0);let a=await S.FH.get("/sharing/shares/my",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});a.data.success&&h(a.data.data)}catch(a){console.error("Error loading shares:",a),b({title:"Error",description:"Failed to load shares",variant:"destructive"})}finally{m(!1)}},[b]);let W=async a=>{let c=`${window.location.origin}/shared/${a}`;try{await navigator.clipboard.writeText(c),V(a),b({title:"Copied!",description:"Share link copied to clipboard."}),setTimeout(()=>V(null),2e3)}catch(a){console.error("Failed to copy:",a),b({title:"Error",description:"Failed to copy link. Please copy manually.",variant:"destructive"})}},X=async()=>{if(B)try{(await S.FH.delete(`/sharing/shares/${B.shareToken}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).data.success&&(h(a=>a.filter(a=>a.id!==B.id)),b({title:"Share Deleted",description:"The share link has been deactivated successfully."}))}catch(a){console.error("Error deleting share:",a),b({title:"Error",description:a.response?.data?.message||"Failed to delete share. Please try again.",variant:"destructive"})}finally{A(!1),T(null)}},Y=async a=>{try{(await S.FH.put(`/sharing/shares/${a.shareToken}`,{body:{isActive:!a.isActive},headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).data.success&&(h(b=>b.map(b=>b.id===a.id?{...b,isActive:!b.isActive}:b)),b({title:a.isActive?"Share Deactivated":"Share Activated",description:`The share link has been ${a.isActive?"deactivated":"activated"}.`}))}catch(a){console.error("Error toggling share status:",a),b({title:"Error",description:"Failed to update share status. Please try again.",variant:"destructive"})}},Z=c.filter(a=>{let b=a.expert.name.toLowerCase().includes(p.toLowerCase())||a.expert.description.toLowerCase().includes(p.toLowerCase()),c="all"===t||"active"===t&&a.isActive||"inactive"===t&&!a.isActive;return b&&c});return i?(0,d.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((a,b)=>(0,d.jsx)(g.Zp,{className:"animate-pulse",children:(0,d.jsxs)(g.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4 mb-2"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4 mb-4"}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"}),(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"}),(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"})]})]})},b))}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"My Shares"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Manage your shared expert links and track their performance"})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(K.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,d.jsx)(s.p,{placeholder:"Search shares...",value:p,onChange:a=>q(a.target.value),className:"pl-10 w-64"})]}),(0,d.jsxs)(v,{children:[(0,d.jsx)(w,{children:(0,d.jsxs)(f.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(L.A,{className:"h-4 w-4 mr-2"}),"all"===t?"All":"active"===t?"Active":"Inactive"]})}),(0,d.jsxs)(x,{children:[(0,d.jsx)(y,{onClick:()=>u("all"),children:"All Shares"}),(0,d.jsx)(y,{onClick:()=>u("active"),children:"Active Only"}),(0,d.jsx)(y,{onClick:()=>u("inactive"),children:"Inactive Only"})]})]})]})]}),0===Z.length?(0,d.jsx)(g.Zp,{children:(0,d.jsxs)(g.Wu,{className:"p-12 text-center",children:[(0,d.jsx)(j.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:0===c.length?"No shares created yet":"No shares match your filters"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:0===c.length?"Start sharing your AI experts to track their performance and reach more users.":"Try adjusting your search or filter criteria."}),0===c.length&&(0,d.jsx)(f.$,{onClick:()=>window.location.reload(),children:"Create Your First Share"})]})}):(0,d.jsx)("div",{className:"space-y-4",children:Z.map(a=>(0,d.jsx)(g.Zp,{className:`transition-all ${!a.isActive?"opacity-60":""}`,children:(0,d.jsx)(g.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:a.expert.name}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(r.E,{variant:a.isActive?"default":"secondary",children:a.isActive?"Active":"Inactive"}),a.monitorEnabled&&(0,d.jsxs)(r.E,{variant:"outline",children:[(0,d.jsx)(n.A,{className:"h-3 w-3 mr-1"}),"Monitored"]})]})]}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:a.expert.description}),(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,d.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-blue-600 mb-1",children:[(0,d.jsx)(k.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Clicks"})]}),(0,d.jsx)("p",{className:"text-2xl font-bold text-blue-900",children:a.clickCount})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-green-600 mb-1",children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Chats"})]}),(0,d.jsx)("p",{className:"text-2xl font-bold text-green-900",children:a.conversionCount})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-purple-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-purple-600 mb-1",children:[(0,d.jsx)(n.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Rate"})]}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-purple-900",children:[0===a.clickCount?0:Math.round(a.conversionCount/a.clickCount*100),"%"]})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-gray-600 mb-1",children:[(0,d.jsx)(M.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Created"})]}),(0,d.jsx)("p",{className:"text-sm font-bold text-gray-900",children:new Date(a.createdAt).toLocaleDateString()})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)(s.p,{value:`${window.location.origin}/shared/${a.shareToken}`,readOnly:!0,className:"flex-1 text-sm"}),(0,d.jsx)(f.$,{onClick:()=>W(a.shareToken),variant:"outline",size:"sm",className:"px-3",children:U===a.shareToken?(0,d.jsx)(N.A,{className:"h-4 w-4"}):(0,d.jsx)(O.A,{className:"h-4 w-4"})})]})]}),(0,d.jsxs)(v,{children:[(0,d.jsx)(w,{children:(0,d.jsx)(f.$,{variant:"ghost",size:"sm",children:(0,d.jsx)(P,{className:"h-4 w-4"})})}),(0,d.jsxs)(x,{align:"end",children:[(0,d.jsxs)(y,{onClick:()=>window.open(`/shared/${a.shareToken}`,"_blank"),children:[(0,d.jsx)(Q.A,{className:"h-4 w-4 mr-2"}),"View Share Page"]}),(0,d.jsxs)(y,{onClick:()=>window.open(`/dashboard/shares/${a.shareToken}/analytics`,"_blank"),children:[(0,d.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"View Analytics"]}),(0,d.jsxs)(y,{onClick:()=>Y(a),children:[(0,d.jsx)(k.A,{className:"h-4 w-4 mr-2"}),a.isActive?"Deactivate":"Activate"]}),(0,d.jsxs)(y,{onClick:()=>{T(a),A(!0)},className:"text-red-600",children:[(0,d.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"Delete Share"]})]})]})]})})},a.id))}),(0,d.jsx)(C,{open:z,onOpenChange:A,children:(0,d.jsxs)(D,{children:[(0,d.jsxs)(E,{children:[(0,d.jsx)(F,{children:"Delete Share Link"}),(0,d.jsxs)(G,{children:['Are you sure you want to delete this share link for "',B?.expert.name,'"? This action cannot be undone and the link will no longer be accessible.']})]}),(0,d.jsxs)(H,{children:[(0,d.jsx)(J,{children:"Cancel"}),(0,d.jsx)(I,{onClick:X,className:"bg-red-600 hover:bg-red-700",children:"Delete Share"})]})]})})]})}function U(){let{toast:a}=(0,o.d)();(0,p.E)();let[b,c]=(0,e.useState)(null),[r,s]=(0,e.useState)(!0),[t,u]=(0,e.useState)(!1),[v,w]=(0,e.useState)(0),[x,y]=(0,e.useState)("overview"),z=()=>{w(a=>a+1)};return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("div",{className:"bg-white border-b",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex items-center justify-between py-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Share Management"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"Create, manage, and track your shared AI experts"})]}),(0,d.jsxs)(f.$,{onClick:()=>u(!0),className:"bg-blue-600 hover:bg-blue-700",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Create New Share"]})]})})}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)(h.tU,{value:x,onValueChange:y,className:"space-y-6",children:[(0,d.jsxs)(h.j7,{className:"grid w-full grid-cols-3",children:[(0,d.jsx)(h.Xi,{value:"overview",children:"Overview"}),(0,d.jsx)(h.Xi,{value:"shares",children:"My Shares"}),(0,d.jsx)(h.Xi,{value:"analytics",children:"Analytics"})]}),(0,d.jsxs)(h.av,{value:"overview",className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,d.jsx)(g.Zp,{children:(0,d.jsxs)(g.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-blue-600 mb-2",children:[(0,d.jsx)(j.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"Total Shares"})]}),(0,d.jsx)("p",{className:"text-3xl font-bold text-blue-900",children:r?"...":b?.totalShares||0}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[r?"...":b?.activeShares||0," active"]})]})}),(0,d.jsx)(g.Zp,{children:(0,d.jsxs)(g.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-green-600 mb-2",children:[(0,d.jsx)(k.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"Total Views"})]}),(0,d.jsx)("p",{className:"text-3xl font-bold text-green-900",children:r?"...":b?.totalClicks?.toLocaleString()||0}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Across all shares"})]})}),(0,d.jsx)(g.Zp,{children:(0,d.jsxs)(g.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-purple-600 mb-2",children:[(0,d.jsx)(l.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"Conversations"})]}),(0,d.jsx)("p",{className:"text-3xl font-bold text-purple-900",children:r?"...":b?.totalConversions?.toLocaleString()||0}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Total chats started"})]})}),(0,d.jsx)(g.Zp,{children:(0,d.jsxs)(g.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-orange-600 mb-2",children:[(0,d.jsx)(m.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"Conversion Rate"})]}),(0,d.jsx)("p",{className:"text-3xl font-bold text-orange-900",children:r?"...":`${b?.conversionRate?.toFixed(1)||0}%`}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Views to chats"})]})})]}),b?.topPerformingShare&&(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)(g.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(n.A,{className:"h-5 w-5 text-yellow-500"}),(0,d.jsx)("span",{children:"Top Performing Share"})]}),(0,d.jsx)(g.BT,{children:"Your most successful shared expert this period"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900",children:b.topPerformingShare.expertName}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["Share Token: ",b.topPerformingShare.shareToken]})]}),(0,d.jsx)("div",{className:"text-right",children:(0,d.jsxs)("div",{className:"flex space-x-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:b.topPerformingShare.clicks}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"Views"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-2xl font-bold text-green-600",children:b.topPerformingShare.conversions}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"Chats"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[b.topPerformingShare.clicks>0?Math.round(b.topPerformingShare.conversions/b.topPerformingShare.clicks*100):0,"%"]}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"Rate"})]})]})})]})})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsx)(g.ZB,{children:"Quick Actions"}),(0,d.jsx)(g.BT,{children:"Common tasks for managing your shared experts"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)(f.$,{variant:"outline",className:"h-20 flex-col space-y-2",onClick:()=>u(!0),children:[(0,d.jsx)(i.A,{className:"h-6 w-6"}),(0,d.jsx)("span",{children:"Create New Share"})]}),(0,d.jsxs)(f.$,{variant:"outline",className:"h-20 flex-col space-y-2",onClick:()=>y("shares"),children:[(0,d.jsx)(j.A,{className:"h-6 w-6"}),(0,d.jsx)("span",{children:"Manage Shares"})]}),(0,d.jsxs)(f.$,{variant:"outline",className:"h-20 flex-col space-y-2",onClick:()=>y("analytics"),children:[(0,d.jsx)(n.A,{className:"h-6 w-6"}),(0,d.jsx)("span",{children:"View Analytics"})]})]})})]}),(!b||0===b.totalShares)&&(0,d.jsx)(g.Zp,{className:"border-dashed border-2 border-gray-300",children:(0,d.jsxs)(g.Wu,{className:"p-12 text-center",children:[(0,d.jsx)(j.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Start Sharing Your Experts"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6 max-w-md mx-auto",children:"Share your AI experts with others and track their performance. Create your first share to get started with analytics and insights."}),(0,d.jsxs)(f.$,{onClick:()=>u(!0),size:"lg",className:"bg-blue-600 hover:bg-blue-700",children:[(0,d.jsx)(i.A,{className:"h-5 w-5 mr-2"}),"Create Your First Share"]})]})})]}),(0,d.jsxs)(h.av,{value:"shares",className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"My Shares"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Manage all your shared expert links"})]}),(0,d.jsx)(f.$,{onClick:z,variant:"outline",children:"Refresh"})]}),(0,d.jsx)(T,{refreshTrigger:v})]}),(0,d.jsxs)(h.av,{value:"analytics",className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Analytics Overview"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Detailed insights into your sharing performance"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{className:"text-lg",children:"Performance Metrics"})}),(0,d.jsxs)(g.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Total Shares"}),(0,d.jsx)("span",{className:"font-semibold",children:b?.totalShares||0})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Active Shares"}),(0,d.jsx)("span",{className:"font-semibold",children:b?.activeShares||0})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Total Views"}),(0,d.jsx)("span",{className:"font-semibold",children:b?.totalClicks?.toLocaleString()||0})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Total Chats"}),(0,d.jsx)("span",{className:"font-semibold",children:b?.totalConversions?.toLocaleString()||0})]})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{className:"text-lg",children:"Conversion Insights"})}),(0,d.jsxs)(g.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:[b?.conversionRate?.toFixed(1)||0,"%"]}),(0,d.jsx)("p",{className:"text-gray-600",children:"Overall Conversion Rate"})]}),(0,d.jsx)("div",{className:"pt-4 border-t",children:(0,d.jsxs)("p",{className:"text-sm text-gray-600 text-center",children:[b?.totalConversions||0," conversations started from ",b?.totalClicks||0," total views"]})})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{className:"text-lg",children:"Quick Actions"})}),(0,d.jsxs)(g.Wu,{className:"space-y-3",children:[(0,d.jsx)(f.$,{variant:"outline",className:"w-full",onClick:()=>y("shares"),children:"View All Shares"}),(0,d.jsx)(f.$,{variant:"outline",className:"w-full",onClick:()=>u(!0),children:"Create New Share"}),(0,d.jsx)(f.$,{variant:"outline",className:"w-full",onClick:z,children:"Refresh Data"})]})]})]}),(0,d.jsx)(g.Zp,{children:(0,d.jsxs)(g.Wu,{className:"p-6 text-center",children:[(0,d.jsx)(n.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Detailed Analytics"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:'For detailed analytics of individual shares, visit the share management section and click on "View Analytics" for any specific share.'}),(0,d.jsx)(f.$,{variant:"outline",onClick:()=>y("shares"),children:"Go to Share Management"})]})})]})]})}),t&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,d.jsx)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Create New Share"}),(0,d.jsx)(f.$,{variant:"ghost",onClick:()=>u(!1),className:"text-gray-500 hover:text-gray-700",children:"\xd7"})]}),(0,d.jsx)(q.A,{expert:{id:0,name:"New Expert",description:"Create a new expert to share",imageUrl:void 0,labels:["general"]},onShareCreated:()=>{u(!1),w(a=>a+1),a({title:"Share Created!",description:"Your expert has been shared successfully."})},onCancel:()=>u(!1)})]})})})]})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},25101:(a,b,c)=>{Promise.resolve().then(c.bind(c,6142))},25334:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},39743:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["shares",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,97718)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/shares/page",pathname:"/dashboard/shares",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/shares/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72085:(a,b,c)=>{"use strict";c.d(b,{E:()=>g,_:()=>h});var d=c(43210),e=c(29867),f=c(62185);function g(){let{toast:a}=(0,e.d)(),[b,c]=(0,d.useState)(!1),[g,h]=(0,d.useState)(null),i=(0,d.useCallback)(async b=>{try{c(!0),h(null);let d=await f.FH.post("/sharing/shares",{body:b,headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(d.data.success){let b=d.data.data,c=`${window.location.origin}/shared/${b.shareToken}`;return a({title:"Share Created!",description:"Your expert share link has been created successfully."}),{...b,shareUrl:c}}throw Error(d.data.message||"Failed to create share")}catch(c){let b=c.response?.data?.message||c.message||"Failed to create share";return h(b),a({title:"Error",description:b,variant:"destructive"}),null}finally{c(!1)}},[a]),j=(0,d.useCallback)(async b=>{try{c(!0),h(null);let a=await f.FH.get(`/sharing/shared/${b}`);if(a.data.success)return a.data.data;throw Error(a.data.message||"Failed to load shared expert")}catch(c){let b=c.response?.data?.message||c.message||"Failed to load shared expert";return h(b),c.response?.status!==404&&a({title:"Error",description:b,variant:"destructive"}),null}finally{c(!1)}},[a]),k=(0,d.useCallback)(async(a,b)=>{try{return(await f.FH.post(`/sharing/track/${a}/click`,{body:{metadata:{userAgent:navigator.userAgent,referrer:document.referrer,timestamp:new Date().toISOString(),...b}}})).data.success}catch(a){return console.error("Failed to track click:",a),!1}},[]),l=(0,d.useCallback)(async(a,b)=>{try{return(await f.FH.post(`/sharing/track/${a}/conversion`,{body:{metadata:{timestamp:new Date().toISOString(),...b}},headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).data.success}catch(a){return console.error("Failed to track conversion:",a),!1}},[]),m=(0,d.useCallback)(async a=>{try{let b=await f.FH.get(`/sharing/consent/${a}`);if(b.data.success)return b.data.data;return null}catch(a){return console.error("Failed to get consent:",a),null}},[]),n=(0,d.useCallback)(async(b,c,d=!0)=>{try{if((await f.FH.post(`/sharing/consent/${b}`,{body:{consent:c,trackingEnabled:d}})).data.success)return a({title:c?"Consent Granted":"Consent Withdrawn",description:c?"Thank you for allowing us to improve your experience.":"Your privacy preferences have been updated."}),!0;return!1}catch(b){return console.error("Failed to set consent:",b),a({title:"Error",description:"Failed to update consent preferences.",variant:"destructive"}),!1}},[a]),o=(0,d.useCallback)(async(b,d)=>{try{c(!0),h(null);let e=await f.FH.put(`/sharing/shares/${b}`,{body:d,headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(e.data.success)return a({title:"Share Updated",description:"Your share settings have been updated successfully."}),!0;throw Error(e.data.message||"Failed to update share")}catch(c){let b=c.response?.data?.message||c.message||"Failed to update share";return h(b),a({title:"Error",description:b,variant:"destructive"}),!1}finally{c(!1)}},[a]),p=(0,d.useCallback)(async b=>{try{c(!0),h(null);let d=await f.FH.delete(`/sharing/shares/${b}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(d.data.success)return a({title:"Share Deleted",description:"The share link has been deactivated successfully."}),!0;throw Error(d.data.message||"Failed to delete share")}catch(c){let b=c.response?.data?.message||c.message||"Failed to delete share";return h(b),a({title:"Error",description:b,variant:"destructive"}),!1}finally{c(!1)}},[a]),q=(0,d.useCallback)(async()=>{try{c(!0),h(null);let a=await f.FH.get("/sharing/shares/my",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(a.data.success)return a.data.data;throw Error(a.data.message||"Failed to load shares")}catch(c){let b=c.response?.data?.message||c.message||"Failed to load shares";return h(b),a({title:"Error",description:b,variant:"destructive"}),null}finally{c(!1)}},[a]),r=(0,d.useCallback)(async(b,d="7d")=>{try{c(!0),h(null);let a=await f.FH.get(`/sharing/analytics/${b}?timeRange=${d}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(a.data.success)return a.data.data;throw Error(a.data.message||"Failed to load analytics")}catch(c){let b=c.response?.data?.message||c.message||"Failed to load analytics";return h(b),a({title:"Error",description:b,variant:"destructive"}),null}finally{c(!1)}},[a]),s=(0,d.useCallback)(async b=>{try{let c=`${window.location.origin}/shared/${b}`;return await navigator.clipboard.writeText(c),a({title:"Copied!",description:"Share link copied to clipboard."}),!0}catch(b){return console.error("Failed to copy to clipboard:",b),a({title:"Error",description:"Failed to copy link. Please copy manually.",variant:"destructive"}),!1}},[a]);return{loading:b,error:g,createShare:i,getSharedExpert:j,trackClick:k,trackConversion:l,getConsent:m,setConsent:n,updateShare:o,deleteShare:p,getUserShares:q,getAnalytics:r,copyShareUrl:s,generateShareUrl:(0,d.useCallback)(a=>`${window.location.origin}/shared/${a}`,[]),clearError:(0,d.useCallback)(()=>{h(null)},[])}}function h(a){let[b,c]=(0,d.useState)(a||null),[e,f]=(0,d.useState)(null),[h,i]=(0,d.useState)(null),j=g(),k=(0,d.useCallback)(async a=>{c(a);let b=await j.getSharedExpert(a);return f(b),b},[j]);return{shareToken:b,shareData:e,consentData:h,loadSharedExpert:k,loadConsent:(0,d.useCallback)(async a=>{let b=await j.getConsent(a);return i(b),b},[j]),handleConsent:(0,d.useCallback)(async(a,b,c=!0)=>{let d=await j.setConsent(a,b,c);return d&&i({hasConsented:b,consentTimestamp:new Date().toISOString(),trackingEnabled:c}),d},[j]),...j}}},74075:a=>{"use strict";a.exports=require("zlib")},77837:(a,b,c)=>{Promise.resolve().then(c.bind(c,97718))},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},80462:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},85763:(a,b,c)=>{"use strict";c.d(b,{Xi:()=>i,av:()=>j,j7:()=>h,tU:()=>g});var d=c(60687);c(43210);var e=c(54304),f=c(4780);function g({className:a,...b}){return(0,d.jsx)(e.bL,{"data-slot":"tabs",className:(0,f.cn)("flex flex-col gap-2",a),...b})}function h({className:a,...b}){return(0,d.jsx)(e.B8,{"data-slot":"tabs-list",className:(0,f.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...b})}function i({className:a,...b}){return(0,d.jsx)(e.l9,{"data-slot":"tabs-trigger",className:(0,f.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...b})}function j({className:a,...b}){return(0,d.jsx)(e.UC,{"data-slot":"tabs-content",className:(0,f.cn)("flex-1 outline-none",a),...b})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88233:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97718:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\Web\\\\pakarai\\\\ai-trainer\\\\fe\\\\src\\\\app\\\\dashboard\\\\shares\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\page.tsx","default")},99270:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,842,928,235,304,263,127],()=>b(b.s=39743));module.exports=c})();