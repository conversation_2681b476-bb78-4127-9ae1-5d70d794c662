"use client";

import React from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FilterOption, FilterType } from "@/types/filters";
import { cn } from "@/lib/utils";

interface FilterSelectProps {
  options: FilterOption[];
  selectedValue: FilterType;
  onSelect: (value: FilterType) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const FilterSelect: React.FC<FilterSelectProps> = ({
  options,
  selectedValue,
  onSelect,
  placeholder = "Select filter",
  className = "",
  disabled = false
}) => {
  const handleValueChange = (value: string) => {
    onSelect(value as FilterType);
  };

  return (
    <Select
      value={selectedValue}
      onValueChange={handleValueChange}
      disabled={disabled}
    >
      <SelectTrigger
        className={cn(
          "w-full sm:w-[200px] h-10 bg-white border border-gray-200 hover:border-gray-300 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        aria-label="Filter experts"
      >
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>

      <SelectContent
        className="bg-white border border-gray-200 shadow-lg rounded-lg p-1 min-w-[200px]"
      >
        {options.map((option) => (
          <SelectItem
            key={option.id}
            value={option.id}
            className={cn(
              "flex items-center gap-3 px-3 py-2.5 rounded-md cursor-pointer transition-colors duration-150",
              "hover:bg-gray-50 focus:bg-gray-50 focus:outline-none",
              "data-[state=checked]:bg-primary/10 data-[state=checked]:text-primary",
              selectedValue === option.id && "bg-primary/5"
            )}
          >
            <span className="text-base flex-shrink-0" role="img" aria-hidden="true">
              {option.icon}
            </span>
            <div className="flex flex-col items-start">
              <span className="font-medium text-gray-900 text-sm">
                {option.label}
              </span>
              {option.description && (
                <span className="text-xs text-gray-500 mt-0.5">
                  {option.description}
                </span>
              )}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};